LX51 LINKER/LOCATER V4.66.93.0                                                          06/04/2025  11:18:48  PAGE 1


LX51 LINKER/LOCATER V4.66.93.0, INVOKED BY:
C:\KEIL_V5\C51\BIN\LX51.EXE STARTUP.obj, main.obj, interrupt.obj, UartDriver.obj TO SH79F083_UART


CPU MODE:     8051 MODE
MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  STARTUP.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.59.0.0
  interrupt.obj (INTERRUPT)
         COMMENT TYPE 0: C51 V9.59.0.0
  UartDriver.obj (UARTDRIVER)
         COMMENT TYPE 0: C51 V9.59.0.0
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?SLDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?ULCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  SH79F083_UART (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   000975H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
I:000000H   I:000000H   I:00007FH   000037H   DATA
C:000000H   C:000000H   C:00FFFFH   00000EH   CONST
X:000000H   X:000000H   X:00FFFFH   00005DH   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000001H.3 BIT


MEMORY MAP OF MODULE:  SH79F083_UART (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000013H   00000CH   BYTE   UNIT     DATA           _DATA_GROUP_
000014H   000015H   000002H   BYTE   UNIT     DATA           ?DT?INTERRUPT
000016H.0 00001FH.7 00000AH.0 ---    ---      **GAP**
000020H.0 000020H.5 000000H.6 BIT    UNIT     BIT            ?BI?INTERRUPT
000020H.6 000021H.2 000000H.5 BIT    UNIT     BIT            ?BI?MAIN
000021H.3 000021H   000000H.5 ---    ---      **GAP**
000022H   000042H   000021H   BYTE   UNIT     DATA           ?DT?MAIN
000043H   000043H   000001H   BYTE   UNIT     IDATA          ?STACK

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00003
000006H   00000AH   000005H   BYTE   UNIT     CODE           ?PR?EX1_INT?INTERRUPT
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0000B
00000EH   000012H   000005H   BYTE   UNIT     CODE           ?PR?TIMER1_INT?INTERRUPT
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 2


000013H   000015H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00013
000016H   00001AH   000005H   BYTE   UNIT     CODE           ?PR?TIMER2_INT?INTERRUPT
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0001B
00001EH   000022H   000005H   BYTE   UNIT     CODE           ?PR?UARTRECEIVEON?UARTDRIVER
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00023
000026H   000029H   000004H   BYTE   UNIT     CODE           ?PR?ADC_INT?INTERRUPT
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?EX0_INT?INTERRUPT
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0002B
00002EH   000031H   000004H   BYTE   UNIT     CODE           ?PR?SCM_INT?INTERRUPT
000032H   000032H   000001H   ---    ---      **GAP**
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00033
000036H   000045H   000010H   BYTE   UNIT     CODE           ?PR?UARTINIT?UARTDRIVER
000046H   000049H   000004H   BYTE   UNIT     CODE           ?PR?PWM_INT?INTERRUPT
00004AH   00004AH   000001H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0004B
00004EH   000056H   000009H   BYTE   UNIT     CODE           ?PR?EX2_INT?INTERRUPT
000057H   00005AH   000004H   BYTE   UNIT     CODE           ?PR?ELPD_INT?INTERRUPT
00005BH   00005DH   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?0005B
00005EH   000061H   000004H   BYTE   UNIT     CODE           ?PR?UARTINTDISABLE?UARTDRIVER
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00063
000066H   00006CH   000007H   BYTE   UNIT     CODE           ?PR?UARTSENDON?UARTDRIVER
00006DH   000070H   000004H   BYTE   UNIT     CODE           ?PR?UARTINTENABLE?UARTDRIVER
000071H   000072H   000002H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?INTERRUPT?00073
000076H   000226H   0001B1H   BYTE   UNIT     CODE           ?PR?EUART0_INT?INTERRUPT
000227H   00035FH   000139H   BYTE   UNIT     CODE           ?PR?LEDPROC?MAIN
000360H   00047DH   00011EH   BYTE   UNIT     CODE           ?C?LIB_CODE
00047EH   000509H   00008CH   BYTE   UNIT     CODE           ?C_C51STARTUP
00050AH   000589H   000080H   BYTE   UNIT     CODE           ?PR?SCANBUTTON?MAIN
00058AH   0005FDH   000074H   BYTE   UNIT     CODE           ?PR?USEDATAINIT?MAIN
0005FEH   000671H   000074H   BYTE   UNIT     CODE           ?PR?TIMER0_INT?INTERRUPT
000672H   0006E1H   000070H   BYTE   UNIT     CODE           ?PR?UART0ACK?MAIN
0006E2H   00074CH   00006BH   BYTE   UNIT     CODE           ?C_INITSEG
00074DH   000790H   000044H   BYTE   UNIT     CODE           ?C?LDIV
000791H   0007D3H   000043H   BYTE   UNIT     CODE           ?PR?SYSINIT?MAIN
0007D4H   000816H   000043H   BYTE   UNIT     CODE           ?PR?UART0DECODE?MAIN
000817H   000854H   00003EH   BYTE   UNIT     CODE           ?PR?_CALCHECKSUM1?INTERRUPT
000855H   000890H   00003CH   BYTE   UNIT     CODE           ?PR?_UARTBAUDSET?UARTDRIVER
000891H   0008C2H   000032H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0008C3H   0008EAH   000028H   BYTE   UNIT     CODE           ?PR?_CALCHECKSUM?MAIN
0008EBH   00090DH   000023H   BYTE   UNIT     CODE           ?PR?INITLEDTESTMODE?MAIN
00090EH   00092DH   000020H   BYTE   UNIT     CODE           ?PR?CLR_DATARAM?MAIN
00092EH   000947H   00001AH   BYTE   UNIT     CODE           ?PR?T0INITIAL?MAIN
000948H   000961H   00001AH   BYTE   UNIT     CODE           ?PR?T1INITIAL?MAIN
000962H   000979H   000018H   BYTE   UNIT     CODE           ?PR?_UARTCONF?UARTDRIVER
00097AH   000987H   00000EH   BYTE   UNIT     CONST          ?CO?MAIN

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000048H   000049H   BYTE   UNIT     XDATA          ?XD?INTERRUPT
000049H   00005AH   000012H   BYTE   UNIT     XDATA          ?XD?MAIN
00005BH   00005CH   000002H   BYTE   UNIT     XDATA          _XDATA_GROUP_



OVERLAY MAP OF MODULE:   SH79F083_UART (?C_STARTUP)


FUNCTION/MODULE                DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE     START  STOP  START  STOP
=======================================================
?C_C51STARTUP                  ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 3


MAIN/MAIN                      ----- -----  ----- -----
  +--> UARTINTDISABLE/UARTDRIVER
  +--> USEDATAINIT/MAIN
  +--> SYSINIT/MAIN
  +--> UARTINIT/UARTDRIVER
  +--> _UARTBAUDSET/UARTDRIVER
  +--> _UARTCONF/UARTDRIVER
  +--> UARTINTENABLE/UARTDRIVER
  +--> INITLEDTESTMODE/MAIN
  +--> SCANBUTTON/MAIN
  +--> UART0DECODE/MAIN
  +--> UART0ACK/MAIN

UARTINTDISABLE/UARTDRIVER      ----- -----  ----- -----

USEDATAINIT/MAIN               ----- -----  ----- -----

SYSINIT/MAIN                   ----- -----  ----- -----
  +--> T0INITIAL/MAIN
  +--> T1INITIAL/MAIN
  +--> CLR_DATARAM/MAIN

T0INITIAL/MAIN                 ----- -----  ----- -----

T1INITIAL/MAIN                 ----- -----  ----- -----

CLR_DATARAM/MAIN               ----- -----  ----- -----

UARTINIT/UARTDRIVER            ----- -----  ----- -----

_UARTBAUDSET/UARTDRIVER        ----- -----  005BH 005CH

_UARTCONF/UARTDRIVER           ----- -----  ----- -----
  +--> UARTINIT/UARTDRIVER
  +--> UARTSENDON/UARTDRIVER
  +--> UARTRECEIVEON/UARTDRIVER

UARTSENDON/UARTDRIVER          ----- -----  ----- -----

UARTRECEIVEON/UARTDRIVER       ----- -----  ----- -----

UARTINTENABLE/UARTDRIVER       ----- -----  ----- -----

INITLEDTESTMODE/MAIN           ----- -----  ----- -----

SCANBUTTON/MAIN                ----- -----  005BH 005BH

UART0DECODE/MAIN               ----- -----  ----- -----
  +--> _CALCHECKSUM/MAIN

_CALCHECKSUM/MAIN              0008H 000CH  ----- -----

UART0ACK/MAIN                  ----- -----  ----- -----

?C_INITSEG                     ----- -----  ----- -----

*** NEW ROOT *****************

EX0_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

TIMER0_INT/INTERRUPT           ----- -----  ----- -----
  +--> LEDPROC/MAIN

LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 4


LEDPROC/MAIN                   ----- -----  ----- -----

*** NEW ROOT *****************

EX1_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

TIMER1_INT/INTERRUPT           ----- -----  ----- -----

*** NEW ROOT *****************

EUART0_INT/INTERRUPT           000DH 000EH  ----- -----
  +--> _CALCHECKSUM1/INTERRUPT

_CALCHECKSUM1/INTERRUPT        000FH 0013H  ----- -----

*** NEW ROOT *****************

TIMER2_INT/INTERRUPT           ----- -----  ----- -----

*** NEW ROOT *****************

ADC_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

EX2_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

SCM_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

PWM_INT/INTERRUPT              ----- -----  ----- -----

*** NEW ROOT *****************

ELPD_INT/INTERRUPT             ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  SH79F083_UART (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      0000000FH   DATA     BYTE      ?_CalCheckSum1?BYTE
      00000008H   DATA     BYTE      ?_CalCheckSum?BYTE
      01000360H   CODE     ---       ?C?CLDOPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      0100038DH   CODE     ---       ?C?CSTPTR
      0100074DH   CODE     ---       ?C?SLDIV
      0100039FH   CODE     ---       ?C?ULCMP
      010003ECH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010004C5H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      010008C3H   CODE     ---       _CalCheckSum
      0100082DH   CODE     ---       _CalCheckSum1
      01000855H   CODE     ---       _UartBaudSet
      01000962H   CODE     ---       _UartConf
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 5


      01000026H   CODE     ---       ADC_INT
*SFR* 00000095H   DATA     BYTE      ADCH
*SFR* 00000093H   DATA     BYTE      ADCON
*SFR* 00000097H   DATA     BYTE      ADDH
*SFR* 00000096H   DATA     BYTE      ADDL
*SFR* 00000094H   DATA     BYTE      ADT
*SFR* 000000F1H   DATA     BYTE      AUXC
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000B0H.2 DATA     BIT       BUTTON
      02000055H   XDATA    BYTE      BUTTON_Buff
      02000053H   XDATA    BYTE      BUTTON_Index
      02000058H   XDATA    BYTE      BUTTON_New
      02000056H   XDATA    BYTE      BUTTON_Old
      02000054H   XDATA    BYTE      BUTTON_Value
      02000059H   XDATA    BYTE      BUTTON_Value1
      0200004EH   XDATA    ---       BUTTON_Value_Buff
      0200004BH   XDATA    BYTE      ButtonID
*SFR* 000000C8H.1 DATA     BIT       C_T2
      02000057H   XDATA    BYTE      Check
*SFR* 000000B2H   DATA     BYTE      CLKCON
      0100090EH   CODE     ---       CLR_DataRAM
*SFR* 000000C8H.0 DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000A8H.6 DATA     BIT       EADC
      01000057H   CODE     ---       ELPD_INT
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
      01000076H   CODE     ---       EUART0_INT
*SFR* 000000A8H.0 DATA     BIT       EX0
      0100002AH   CODE     ---       EX0_INT
*SFR* 000000A8H.2 DATA     BIT       EX1
      01000006H   CODE     ---       EX1_INT
      0100004EH   CODE     ---       EX2_INT
*SFR* 000000C8H.3 DATA     BIT       EXEN2
*SFR* 000000E8H   DATA     BYTE      EXF0
*SFR* 000000C8H.6 DATA     BIT       EXF2
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000A7H   DATA     BYTE      FLASHCON
      02000000H   XDATA    BYTE      gb1msCnt
      02000002H   XDATA    BYTE      gb500msCnt485
      00000021H.2 BIT      BIT       gb_TestMode
      02000049H   XDATA    WORD      gb_TestModeCount3s
      00000032H   DATA     BYTE      gB_Uart0_1ms
      00000020H.5 BIT      BIT       gbFg10msCH1
      00000020H.3 BIT      BIT       gbFg5msCH1
      02000052H   XDATA    BYTE      gBFg_100ms
      00000021H.1 BIT      BIT       gBFg_1ms
      00000020H.7 BIT      BIT       gbFg_BUTTON_Flag
      00000021H.0 BIT      BIT       gBFg_ScanButton_1ms
      00000020H.6 BIT      BIT       gBFg_Uart0_1ms_ack
      00000020H.2 BIT      BIT       gbFg_Uart0Ack
      00000020H.0 BIT      BIT       gbFg_Uart0RxEnd
      00000020H.4 BIT      BIT       gbFg_Uart0SendRec
      00000020H.1 BIT      BIT       gbFg_Uart1ReOk
      02000005H   XDATA    ---       gbUart0Buff
      02000027H   XDATA    ---       gbUart0Buff1
      02000004H   XDATA    BYTE      gbUart0Count500ms
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 6


      02000003H   XDATA    BYTE      gbUart0Head
      02000026H   XDATA    BYTE      gbUart0Len
      02000001H   XDATA    BYTE      gbUart0RxLen
      02000048H   XDATA    BYTE      gbUart0TxdLen
*SFR* 000000F2H   DATA     BYTE      IB_CON1
*SFR* 000000F3H   DATA     BYTE      IB_CON2
*SFR* 000000F4H   DATA     BYTE      IB_CON3
*SFR* 000000F5H   DATA     BYTE      IB_CON4
*SFR* 000000F6H   DATA     BYTE      IB_CON5
*SFR* 000000FCH   DATA     BYTE      IB_DATA
*SFR* 000000FBH   DATA     BYTE      IB_OFFSET
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000E8H.0 DATA     BIT       IE2
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000A9H   DATA     BYTE      IEN1
      010008EBH   CODE     ---       InitLedTestMode
*SFR* 00000086H   DATA     BYTE      INSCON
*SFR* 000000B4H   DATA     BYTE      IPH0
*SFR* 000000B5H   DATA     BYTE      IPH1
*SFR* 000000B8H   DATA     BYTE      IPL0
*SFR* 000000B9H   DATA     BYTE      IPL1
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000E8H.2 DATA     BIT       IT20
*SFR* 000000E8H.3 DATA     BIT       IT21
*SFR* 00000090H.7 DATA     BIT       LED1
      0000003BH   DATA     DWORD     LED1_Count
      00000022H   DATA     WORD      LED1_Off
      00000033H   DATA     DWORD     LED1_Off_Count
      0000002EH   DATA     WORD      LED1_On
      00000026H   DATA     DWORD     LED1_On_Count
*SFR* 00000090H.6 DATA     BIT       LED2
      0000003FH   DATA     DWORD     LED2_Count
      00000024H   DATA     WORD      LED2_Off
      00000037H   DATA     DWORD     LED2_Off_Count
      00000030H   DATA     WORD      LED2_On
      0000002AH   DATA     DWORD     LED2_On_Count
      01000227H   CODE     ---       LEDProc
      0200005AH   XDATA    BYTE      Lenth
      00000015H   DATA     BYTE      loop_20ms_exit
*SFR* 000000B3H   DATA     BYTE      LPDCON
      01000891H   CODE     ---       main
      0200004CH   XDATA    BYTE      OBJ_CMD
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.2 DATA     BIT       P1_2
*SFR* 00000090H.3 DATA     BIT       P1_3
*SFR* 00000090H.4 DATA     BIT       P1_4
*SFR* 00000090H.5 DATA     BIT       P1_5
*SFR* 00000090H.6 DATA     BIT       P1_6
*SFR* 00000090H.7 DATA     BIT       P1_7
*SFR* 000000EAH   DATA     BYTE      P1M0
*SFR* 000000E2H   DATA     BYTE      P1M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P3_0
*SFR* 000000B0H.1 DATA     BIT       P3_1
*SFR* 000000B0H.2 DATA     BIT       P3_2
*SFR* 000000B0H.3 DATA     BIT       P3_3
*SFR* 000000B0H.7 DATA     BIT       P3_7
*SFR* 000000ECH   DATA     BYTE      P3M0
*SFR* 000000E4H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P4_0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 7


*SFR* 000000C0H.1 DATA     BIT       P4_1
*SFR* 000000C0H.2 DATA     BIT       P4_2
*SFR* 000000EDH   DATA     BYTE      P4M0
*SFR* 000000E5H   DATA     BYTE      P4M1
*SFR* 000000B8H.6 DATA     BIT       PADCL
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B8H.4 DATA     BIT       PSL
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0L
*SFR* 000000B8H.3 DATA     BIT       PT1L
*SFR* 000000B8H.5 DATA     BIT       PT2L
      01000046H   CODE     ---       PWM_INT
*SFR* 000000D1H   DATA     BYTE      PWMCON
*SFR* 000000D3H   DATA     BYTE      PWMD
*SFR* 000000D2H   DATA     BYTE      PWMP
*SFR* 000000B8H.0 DATA     BIT       PX0L
*SFR* 000000B8H.2 DATA     BIT       PX1L
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000CBH   DATA     BYTE      RCAP2H
*SFR* 000000CAH   DATA     BYTE      RCAP2L
*SFR* 000000C8H.5 DATA     BIT       RCLK
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 000000B1H   DATA     BYTE      RSTSTAT
*SFR* 0000009AH   DATA     BYTE      SADDR
*SFR* 0000009BH   DATA     BYTE      SADEN
*SFR* 00000099H   DATA     BYTE      SBUF
      00000014H   DATA     BYTE      SBUFTemp
      0100050AH   CODE     ---       ScanButton
      0100002EH   CODE     ---       SCM_INT
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 00000098H.7 DATA     BIT       SM0_FE
*SFR* 00000098H.6 DATA     BIT       SM1_RXOV
*SFR* 00000098H.5 DATA     BIT       SM2_TXCOL
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 0000008EH   DATA     BYTE      SUSLO
      01000791H   CODE     ---       SysInit
      0100092EH   CODE     ---       T0Initial
      01000948H   CODE     ---       T1Initial
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C9H   DATA     BYTE      T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.4 DATA     BIT       TCLK
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 000000CEH   DATA     BYTE      TCON1
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C8H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 00000098H.1 DATA     BIT       TI
      010005FEH   CODE     ---       Timer0_INT
      0100000EH   CODE     ---       Timer1_INT
      01000016H   CODE     ---       Timer2_INT
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C8H.2 DATA     BIT       TR2
      01000672H   CODE     ---       Uart0Ack
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 8


      010007D4H   CODE     ---       Uart0Decode
      01000036H   CODE     ---       UartInit
      0100005EH   CODE     ---       UartIntDisable
      0100006DH   CODE     ---       UartIntEnable
      0100001EH   CODE     ---       UARTReceiveOn
      01000066H   CODE     ---       UARTSendOn
      0200004DH   XDATA    BYTE      UpOrDown
      0100058AH   CODE     ---       UseDataInit
*SFR* 000000F7H   DATA     BYTE      XPAGE



SYMBOL TABLE OF MODULE:  SH79F083_UART (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000080H   SYMBOL    NUMBER   ---       IDATALEN
      01000481H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      0100047EH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XDATALEN
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      0100047EH   LINE      CODE     ---       #133
      01000480H   LINE      CODE     ---       #134
      01000481H   LINE      CODE     ---       #135
      01000482H   LINE      CODE     ---       #136
      01000484H   LINE      CODE     ---       #185
      01000487H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       MAIN
      0200005AH   PUBLIC    XDATA    BYTE      Lenth
      0000003FH   PUBLIC    DATA     DWORD     LED2_Count
      0000003BH   PUBLIC    DATA     DWORD     LED1_Count
      00000037H   PUBLIC    DATA     DWORD     LED2_Off_Count
      00000033H   PUBLIC    DATA     DWORD     LED1_Off_Count
      02000059H   PUBLIC    XDATA    BYTE      BUTTON_Value1
      02000058H   PUBLIC    XDATA    BYTE      BUTTON_New
      02000057H   PUBLIC    XDATA    BYTE      Check
      02000056H   PUBLIC    XDATA    BYTE      BUTTON_Old
      00000021H.2 PUBLIC    BIT      BIT       gb_TestMode
      00000032H   PUBLIC    DATA     BYTE      gB_Uart0_1ms
      00000021H.1 PUBLIC    BIT      BIT       gBFg_1ms
      02000055H   PUBLIC    XDATA    BYTE      BUTTON_Buff
      02000054H   PUBLIC    XDATA    BYTE      BUTTON_Value
      02000053H   PUBLIC    XDATA    BYTE      BUTTON_Index
      00000021H.0 PUBLIC    BIT      BIT       gBFg_ScanButton_1ms
      02000052H   PUBLIC    XDATA    BYTE      gBFg_100ms
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 9


      00000030H   PUBLIC    DATA     WORD      LED2_On
      0000002EH   PUBLIC    DATA     WORD      LED1_On
      0200004EH   PUBLIC    XDATA    ---       BUTTON_Value_Buff
      0200004DH   PUBLIC    XDATA    BYTE      UpOrDown
      00000020H.7 PUBLIC    BIT      BIT       gbFg_BUTTON_Flag
      0000002AH   PUBLIC    DATA     DWORD     LED2_On_Count
      00000026H   PUBLIC    DATA     DWORD     LED1_On_Count
      0200004CH   PUBLIC    XDATA    BYTE      OBJ_CMD
      0200004BH   PUBLIC    XDATA    BYTE      ButtonID
      00000020H.6 PUBLIC    BIT      BIT       gBFg_Uart0_1ms_ack
      02000049H   PUBLIC    XDATA    WORD      gb_TestModeCount3s
      00000024H   PUBLIC    DATA     WORD      LED2_Off
      00000022H   PUBLIC    DATA     WORD      LED1_Off
      01000891H   PUBLIC    CODE     ---       main
      01000672H   PUBLIC    CODE     ---       Uart0Ack
      010007D4H   PUBLIC    CODE     ---       Uart0Decode
      00000008H   PUBLIC    DATA     BYTE      ?_CalCheckSum?BYTE
      010008C3H   PUBLIC    CODE     ---       _CalCheckSum
      0100050AH   PUBLIC    CODE     ---       ScanButton
      01000227H   PUBLIC    CODE     ---       LEDProc
      010008EBH   PUBLIC    CODE     ---       InitLedTestMode
      01000948H   PUBLIC    CODE     ---       T1Initial
      0100092EH   PUBLIC    CODE     ---       T0Initial
      0100058AH   PUBLIC    CODE     ---       UseDataInit
      01000791H   PUBLIC    CODE     ---       SysInit
      0100090EH   PUBLIC    CODE     ---       CLR_DataRAM
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      00000090H.7 SFRSYM    DATA     BIT       LED1
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      00000090H.6 SFRSYM    DATA     BIT       LED2
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 10


      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 11


      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000B0H.2 SFRSYM    DATA     BIT       BUTTON
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      0100090EH   BLOCK     CODE     ---       LVL=0
      0100090EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000001H   SYMBOL    DATA     ---       p
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100090EH   LINE      CODE     ---       #140
      0100090EH   LINE      CODE     ---       #141
      0100090EH   LINE      CODE     ---       #145
      01000914H   LINE      CODE     ---       #146
      0100092DH   LINE      CODE     ---       #147
      ---         BLOCKEND  ---      ---       LVL=0

      01000791H   BLOCK     CODE     ---       LVL=0
      01000791H   LINE      CODE     ---       #162
      01000791H   LINE      CODE     ---       #163
      01000791H   LINE      CODE     ---       #164
      01000793H   LINE      CODE     ---       #166
      01000796H   LINE      CODE     ---       #167
      01000799H   LINE      CODE     ---       #168
      0100079BH   LINE      CODE     ---       #170
      0100079EH   LINE      CODE     ---       #171
      010007A1H   LINE      CODE     ---       #173
      010007A3H   LINE      CODE     ---       #174
      010007A6H   LINE      CODE     ---       #176
      010007A8H   LINE      CODE     ---       #177
      010007ABH   LINE      CODE     ---       #180
      010007ADH   LINE      CODE     ---       #187
      010007B0H   LINE      CODE     ---       #188
      010007B3H   LINE      CODE     ---       #189
      010007B5H   LINE      CODE     ---       #190
      010007B7H   LINE      CODE     ---       #194
      010007BAH   LINE      CODE     ---       #197
      010007BDH   LINE      CODE     ---       #204
      010007C0H   LINE      CODE     ---       #208
      010007C3H   LINE      CODE     ---       #211
      010007C5H   LINE      CODE     ---       #213
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 12


      010007C8H   LINE      CODE     ---       #216
      010007CBH   LINE      CODE     ---       #217
      010007CEH   LINE      CODE     ---       #219
      010007D1H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      0100058AH   BLOCK     CODE     ---       LVL=0
      0100058AH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100058AH   LINE      CODE     ---       #251
      0100058AH   LINE      CODE     ---       #252
      0100058AH   LINE      CODE     ---       #255
      0100058FH   LINE      CODE     ---       #256
      01000593H   LINE      CODE     ---       #257
      01000595H   LINE      CODE     ---       #258
      01000597H   LINE      CODE     ---       #259
      010005A2H   LINE      CODE     ---       #260
      010005A2H   LINE      CODE     ---       #261
      010005AEH   LINE      CODE     ---       #262
      010005B1H   LINE      CODE     ---       #264
      010005B6H   LINE      CODE     ---       #265
      010005BAH   LINE      CODE     ---       #266
      010005BEH   LINE      CODE     ---       #267
      010005C2H   LINE      CODE     ---       #269
      010005C4H   LINE      CODE     ---       #270
      010005C8H   LINE      CODE     ---       #272
      010005CCH   LINE      CODE     ---       #273
      010005D0H   LINE      CODE     ---       #274
      010005D4H   LINE      CODE     ---       #275
      010005D8H   LINE      CODE     ---       #276
      010005DCH   LINE      CODE     ---       #277
      010005E0H   LINE      CODE     ---       #278
      010005E2H   LINE      CODE     ---       #279
      010005E4H   LINE      CODE     ---       #280
      010005E6H   LINE      CODE     ---       #282
      010005ECH   LINE      CODE     ---       #283
      010005ECH   LINE      CODE     ---       #284
      010005ECH   LINE      CODE     ---       #285
      010005ECH   LINE      CODE     ---       #286
      010005ECH   LINE      CODE     ---       #287
      010005ECH   LINE      CODE     ---       #288
      010005EEH   LINE      CODE     ---       #290
      010005EEH   LINE      CODE     ---       #291
      010005F1H   LINE      CODE     ---       #292
      010005F5H   LINE      CODE     ---       #293
      010005F9H   LINE      CODE     ---       #294
      010005FDH   LINE      CODE     ---       #295
      010005FDH   LINE      CODE     ---       #296
      ---         BLOCKEND  ---      ---       LVL=0

      0100092EH   BLOCK     CODE     ---       LVL=0
      0100092EH   LINE      CODE     ---       #301
      0100092EH   LINE      CODE     ---       #302
      0100092EH   LINE      CODE     ---       #303
      01000931H   LINE      CODE     ---       #305
      01000934H   LINE      CODE     ---       #306
      01000937H   LINE      CODE     ---       #309
      0100093AH   LINE      CODE     ---       #310
      0100093DH   LINE      CODE     ---       #313
      01000940H   LINE      CODE     ---       #314
      01000943H   LINE      CODE     ---       #315
      01000945H   LINE      CODE     ---       #316
      01000947H   LINE      CODE     ---       #317
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 13



      01000948H   BLOCK     CODE     ---       LVL=0
      01000948H   LINE      CODE     ---       #321
      01000948H   LINE      CODE     ---       #322
      01000948H   LINE      CODE     ---       #323
      0100094BH   LINE      CODE     ---       #325
      0100094EH   LINE      CODE     ---       #326
      01000951H   LINE      CODE     ---       #329
      01000954H   LINE      CODE     ---       #330
      01000957H   LINE      CODE     ---       #333
      0100095AH   LINE      CODE     ---       #334
      0100095DH   LINE      CODE     ---       #335
      0100095FH   LINE      CODE     ---       #336
      01000961H   LINE      CODE     ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      010008EBH   BLOCK     CODE     ---       LVL=0
      010008EBH   LINE      CODE     ---       #341
      010008EBH   LINE      CODE     ---       #342
      010008EBH   LINE      CODE     ---       #343
      010008EEH   LINE      CODE     ---       #344
      010008EEH   LINE      CODE     ---       #345
      010008F0H   LINE      CODE     ---       #346
      010008F7H   LINE      CODE     ---       #347
      010008FAH   LINE      CODE     ---       #348
      01000902H   LINE      CODE     ---       #349
      01000905H   LINE      CODE     ---       #350
      0100090DH   LINE      CODE     ---       #351
      0100090DH   LINE      CODE     ---       #352
      ---         BLOCKEND  ---      ---       LVL=0

      01000227H   BLOCK     CODE     ---       LVL=0
      01000227H   LINE      CODE     ---       #356
      01000227H   LINE      CODE     ---       #357
      01000227H   LINE      CODE     ---       #358
      0100022DH   LINE      CODE     ---       #359
      0100022DH   LINE      CODE     ---       #360
      0100022FH   LINE      CODE     ---       #362
      01000235H   LINE      CODE     ---       #363
      01000242H   LINE      CODE     ---       #364
      01000242H   LINE      CODE     ---       #365
      01000244H   LINE      CODE     ---       #373
      0100024AH   LINE      CODE     ---       #374
      0100024AH   LINE      CODE     ---       #375
      01000258H   LINE      CODE     ---       #376
      01000267H   LINE      CODE     ---       #377
      01000267H   LINE      CODE     ---       #378
      0100026BH   LINE      CODE     ---       #379
      0100026DH   LINE      CODE     ---       #380
      0100026EH   LINE      CODE     ---       #382
      0100026EH   LINE      CODE     ---       #383
      01000283H   LINE      CODE     ---       #384
      01000297H   LINE      CODE     ---       #385
      01000297H   LINE      CODE     ---       #386
      01000297H   LINE      CODE     ---       #387
      0100029AH   LINE      CODE     ---       #388
      010002AEH   LINE      CODE     ---       #389
      010002AEH   LINE      CODE     ---       #390
      010002AEH   LINE      CODE     ---       #391
      010002B1H   LINE      CODE     ---       #392
      010002C5H   LINE      CODE     ---       #393
      010002C5H   LINE      CODE     ---       #394
      010002C8H   LINE      CODE     ---       #395
      010002C8H   LINE      CODE     ---       #396
      010002CBH   LINE      CODE     ---       #398
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 14


      010002CBH   LINE      CODE     ---       #399
      010002CCH   LINE      CODE     ---       #400
      010002CCH   LINE      CODE     ---       #401
      010002CCH   LINE      CODE     ---       #402
      010002CFH   LINE      CODE     ---       #405
      010002CFH   LINE      CODE     ---       #406
      010002D5H   LINE      CODE     ---       #407
      010002D5H   LINE      CODE     ---       #408
      010002D5H   LINE      CODE     ---       #409
      010002D5H   LINE      CODE     ---       #410
      010002D7H   LINE      CODE     ---       #411
      010002E3H   LINE      CODE     ---       #412
      010002E3H   LINE      CODE     ---       #413
      010002E6H   LINE      CODE     ---       #414
      010002E6H   LINE      CODE     ---       #415
      010002E8H   LINE      CODE     ---       #417
      010002E8H   LINE      CODE     ---       #418
      010002EEH   LINE      CODE     ---       #419
      010002EEH   LINE      CODE     ---       #420
      010002F1H   LINE      CODE     ---       #421
      010002F1H   LINE      CODE     ---       #422
      010002F3H   LINE      CODE     ---       #424
      010002F3H   LINE      CODE     ---       #425
      01000308H   LINE      CODE     ---       #426
      0100031DH   LINE      CODE     ---       #427
      0100031DH   LINE      CODE     ---       #428
      01000320H   LINE      CODE     ---       #429
      01000321H   LINE      CODE     ---       #430
      01000337H   LINE      CODE     ---       #431
      01000337H   LINE      CODE     ---       #432
      0100033AH   LINE      CODE     ---       #433
      0100033BH   LINE      CODE     ---       #434
      01000351H   LINE      CODE     ---       #435
      01000351H   LINE      CODE     ---       #436
      01000354H   LINE      CODE     ---       #437
      01000354H   LINE      CODE     ---       #438
      01000356H   LINE      CODE     ---       #440
      01000356H   LINE      CODE     ---       #441
      0100035FH   LINE      CODE     ---       #442
      0100035FH   LINE      CODE     ---       #443
      0100035FH   LINE      CODE     ---       #444
      0100035FH   LINE      CODE     ---       #445
      0100035FH   LINE      CODE     ---       #446
      0100035FH   LINE      CODE     ---       #447
      0100035FH   LINE      CODE     ---       #448
      ---         BLOCKEND  ---      ---       LVL=0

      0100050AH   BLOCK     CODE     ---       LVL=0
      0100050AH   BLOCK     CODE     NEAR LAB  LVL=1
      0200005BH   SYMBOL    XDATA    BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100050AH   LINE      CODE     ---       #544
      0100050AH   LINE      CODE     ---       #545
      0100050AH   LINE      CODE     ---       #546
      0100050FH   LINE      CODE     ---       #548
      01000512H   LINE      CODE     ---       #549
      01000512H   LINE      CODE     ---       #550
      01000514H   LINE      CODE     ---       #552
      01000517H   LINE      CODE     ---       #553
      0100051AH   LINE      CODE     ---       #554
      0100051DH   LINE      CODE     ---       #555
      0100051EH   LINE      CODE     ---       #556
      0100051FH   LINE      CODE     ---       #557
      01000520H   LINE      CODE     ---       #560
      01000526H   LINE      CODE     ---       #561
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 15


      01000526H   LINE      CODE     ---       #562
      01000527H   LINE      CODE     ---       #563
      01000529H   LINE      CODE     ---       #565
      01000529H   LINE      CODE     ---       #566
      0100052CH   LINE      CODE     ---       #567
      0100052CH   LINE      CODE     ---       #569
      01000539H   LINE      CODE     ---       #570
      01000539H   LINE      CODE     ---       #571
      0100053BH   LINE      CODE     ---       #572
      0100053CH   LINE      CODE     ---       #574
      0100053CH   LINE      CODE     ---       #575
      01000543H   LINE      CODE     ---       #576
      01000543H   LINE      CODE     ---       #577
      01000549H   LINE      CODE     ---       #578
      01000549H   LINE      CODE     ---       #579
      0100054EH   LINE      CODE     ---       #580
      0100054EH   LINE      CODE     ---       #581
      01000550H   LINE      CODE     ---       #583
      01000550H   LINE      CODE     ---       #584
      01000556H   LINE      CODE     ---       #585
      0100055AH   LINE      CODE     ---       #586
      0100055AH   LINE      CODE     ---       #587
      0100055BH   LINE      CODE     ---       #589
      0100055BH   LINE      CODE     ---       #590
      01000562H   LINE      CODE     ---       #591
      01000562H   LINE      CODE     ---       #592
      01000567H   LINE      CODE     ---       #593
      0100056BH   LINE      CODE     ---       #594
      0100056DH   LINE      CODE     ---       #596
      0100056DH   LINE      CODE     ---       #597
      01000573H   LINE      CODE     ---       #598
      01000577H   LINE      CODE     ---       #600
      0100057DH   LINE      CODE     ---       #601
      0100057DH   LINE      CODE     ---       #602
      0100057FH   LINE      CODE     ---       #603
      01000580H   LINE      CODE     ---       #604
      01000582H   LINE      CODE     ---       #606
      01000582H   LINE      CODE     ---       #607
      01000584H   LINE      CODE     ---       #608
      01000584H   LINE      CODE     ---       #609
      01000584H   LINE      CODE     ---       #610
      01000589H   LINE      CODE     ---       #611
      01000589H   LINE      CODE     ---       #612
      01000589H   LINE      CODE     ---       #613
      01000589H   LINE      CODE     ---       #614
      ---         BLOCKEND  ---      ---       LVL=0

      010008C3H   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    DATA     ---       buff
      00000005H   SYMBOL    DATA     BYTE      buffHeadAdd
      0000000CH   SYMBOL    DATA     BYTE      Len
      010008C9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      010008C3H   LINE      CODE     ---       #618
      010008C9H   LINE      CODE     ---       #619
      010008C9H   LINE      CODE     ---       #622
      010008CBH   LINE      CODE     ---       #623
      010008D7H   LINE      CODE     ---       #624
      010008D7H   LINE      CODE     ---       #625
      010008E7H   LINE      CODE     ---       #626
      010008EAH   LINE      CODE     ---       #627
      010008EAH   LINE      CODE     ---       #628
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 16



      010007D4H   BLOCK     CODE     ---       LVL=0
      010007D4H   LINE      CODE     ---       #671
      010007D4H   LINE      CODE     ---       #672
      010007D4H   LINE      CODE     ---       #674
      010007D7H   LINE      CODE     ---       #675
      010007D7H   LINE      CODE     ---       #676
      010007D9H   LINE      CODE     ---       #678
      010007DCH   LINE      CODE     ---       #680
      010007E2H   LINE      CODE     ---       #681
      010007E6H   LINE      CODE     ---       #682
      010007EAH   LINE      CODE     ---       #683
      010007EEH   LINE      CODE     ---       #684
      010007F2H   LINE      CODE     ---       #685
      010007F6H   LINE      CODE     ---       #686
      010007FAH   LINE      CODE     ---       #687
      010007FEH   LINE      CODE     ---       #688
      01000814H   LINE      CODE     ---       #691
      01000816H   LINE      CODE     ---       #692
      01000816H   LINE      CODE     ---       #693
      ---         BLOCKEND  ---      ---       LVL=0

      01000672H   BLOCK     CODE     ---       LVL=0
      01000672H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000672H   LINE      CODE     ---       #703
      01000672H   LINE      CODE     ---       #704
      01000672H   LINE      CODE     ---       #707
      01000675H   LINE      CODE     ---       #708
      01000675H   LINE      CODE     ---       #709
      01000678H   LINE      CODE     ---       #710
      01000678H   LINE      CODE     ---       #711
      0100067AH   LINE      CODE     ---       #712
      01000685H   LINE      CODE     ---       #713
      01000685H   LINE      CODE     ---       #714
      01000688H   LINE      CODE     ---       #715
      0100068AH   LINE      CODE     ---       #716
      0100068CH   LINE      CODE     ---       #718
      01000694H   LINE      CODE     ---       #719
      010006A0H   LINE      CODE     ---       #720
      010006A0H   LINE      CODE     ---       #721
      010006B8H   LINE      CODE     ---       #722
      010006BBH   LINE      CODE     ---       #724
      010006C7H   LINE      CODE     ---       #725
      010006C7H   LINE      CODE     ---       #726
      010006D3H   LINE      CODE     ---       #727
      010006D6H   LINE      CODE     ---       #729
      010006D8H   LINE      CODE     ---       #730
      010006DDH   LINE      CODE     ---       #731
      010006DFH   LINE      CODE     ---       #732
      010006E1H   LINE      CODE     ---       #733
      010006E1H   LINE      CODE     ---       #734
      010006E1H   LINE      CODE     ---       #735
      010006E1H   LINE      CODE     ---       #736
      ---         BLOCKEND  ---      ---       LVL=0

      01000891H   BLOCK     CODE     ---       LVL=0
      01000891H   LINE      CODE     ---       #742
      01000891H   LINE      CODE     ---       #743
      01000891H   LINE      CODE     ---       #744
      01000893H   LINE      CODE     ---       #745
      01000896H   LINE      CODE     ---       #746
      01000899H   LINE      CODE     ---       #747
      0100089CH   LINE      CODE     ---       #748
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 17


      0100089FH   LINE      CODE     ---       #749
      010008A6H   LINE      CODE     ---       #750
      010008ABH   LINE      CODE     ---       #751
      010008AEH   LINE      CODE     ---       #756
      010008B0H   LINE      CODE     ---       #757
      010008B3H   LINE      CODE     ---       #759
      010008B5H   LINE      CODE     ---       #761
      010008B5H   LINE      CODE     ---       #762
      010008B5H   LINE      CODE     ---       #763
      010008B8H   LINE      CODE     ---       #765
      010008BBH   LINE      CODE     ---       #767
      010008BEH   LINE      CODE     ---       #768
      010008C1H   LINE      CODE     ---       #769
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       INTERRUPT
      00000015H   PUBLIC    DATA     BYTE      loop_20ms_exit
      00000020H.5 PUBLIC    BIT      BIT       gbFg10msCH1
      02000048H   PUBLIC    XDATA    BYTE      gbUart0TxdLen
      02000027H   PUBLIC    XDATA    ---       gbUart0Buff1
      00000020H.4 PUBLIC    BIT      BIT       gbFg_Uart0SendRec
      00000020H.3 PUBLIC    BIT      BIT       gbFg5msCH1
      00000014H   PUBLIC    DATA     BYTE      SBUFTemp
      02000026H   PUBLIC    XDATA    BYTE      gbUart0Len
      00000020H.2 PUBLIC    BIT      BIT       gbFg_Uart0Ack
      02000005H   PUBLIC    XDATA    ---       gbUart0Buff
      02000004H   PUBLIC    XDATA    BYTE      gbUart0Count500ms
      00000020H.1 PUBLIC    BIT      BIT       gbFg_Uart1ReOk
      02000003H   PUBLIC    XDATA    BYTE      gbUart0Head
      00000020H.0 PUBLIC    BIT      BIT       gbFg_Uart0RxEnd
      02000002H   PUBLIC    XDATA    BYTE      gb500msCnt485
      02000001H   PUBLIC    XDATA    BYTE      gbUart0RxLen
      02000000H   PUBLIC    XDATA    BYTE      gb1msCnt
      01000057H   PUBLIC    CODE     ---       ELPD_INT
      01000046H   PUBLIC    CODE     ---       PWM_INT
      0100002EH   PUBLIC    CODE     ---       SCM_INT
      0100004EH   PUBLIC    CODE     ---       EX2_INT
      01000026H   PUBLIC    CODE     ---       ADC_INT
      01000016H   PUBLIC    CODE     ---       Timer2_INT
      01000076H   PUBLIC    CODE     ---       EUART0_INT
      0100000EH   PUBLIC    CODE     ---       Timer1_INT
      01000006H   PUBLIC    CODE     ---       EX1_INT
      010005FEH   PUBLIC    CODE     ---       Timer0_INT
      0100002AH   PUBLIC    CODE     ---       EX0_INT
      0000000FH   PUBLIC    DATA     BYTE      ?_CalCheckSum1?BYTE
      0100082DH   PUBLIC    CODE     ---       _CalCheckSum1
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 18


      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 19


      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000817H   BLOCK     CODE     VOID      LVL=0
      ---         BLOCKEND  ---      ---       LVL=0

      0100082DH   BLOCK     CODE     ---       LVL=0
      0000000FH   SYMBOL    DATA     ---       buff
      00000005H   SYMBOL    DATA     BYTE      buffHeadAdd
      00000013H   SYMBOL    DATA     BYTE      Len
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      01000833H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100082DH   LINE      CODE     ---       #104
      01000833H   LINE      CODE     ---       #105
      01000833H   LINE      CODE     ---       #108
      01000835H   LINE      CODE     ---       #109
      01000841H   LINE      CODE     ---       #110
      01000841H   LINE      CODE     ---       #111
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 20


      01000851H   LINE      CODE     ---       #112
      01000854H   LINE      CODE     ---       #113
      01000854H   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #119
      0100002AH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      010005FEH   BLOCK     CODE     ---       LVL=0
      010005FEH   LINE      CODE     ---       #130
      0100061BH   LINE      CODE     ---       #132
      0100061DH   LINE      CODE     ---       #133
      0100061FH   LINE      CODE     ---       #134
      01000622H   LINE      CODE     ---       #135
      01000625H   LINE      CODE     ---       #136
      01000627H   LINE      CODE     ---       #138
      01000629H   LINE      CODE     ---       #139
      0100062BH   LINE      CODE     ---       #141
      0100062DH   LINE      CODE     ---       #143
      01000633H   LINE      CODE     ---       #144
      0100063DH   LINE      CODE     ---       #145
      0100063DH   LINE      CODE     ---       #146
      0100063FH   LINE      CODE     ---       #147
      01000641H   LINE      CODE     ---       #148
      01000641H   LINE      CODE     ---       #150
      0100064FH   LINE      CODE     ---       #151
      0100064FH   LINE      CODE     ---       #153
      0100064FH   LINE      CODE     ---       #154
      01000652H   LINE      CODE     ---       #155
      01000652H   LINE      CODE     ---       #161
      01000652H   LINE      CODE     ---       #162
      01000652H   LINE      CODE     ---       #164
      01000652H   LINE      CODE     ---       #165
      01000654H   LINE      CODE     ---       #166
      01000654H   LINE      CODE     ---       #168
      01000657H   LINE      CODE     ---       #169
      ---         BLOCKEND  ---      ---       LVL=0

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #173
      01000006H   LINE      CODE     ---       #175
      01000008H   LINE      CODE     ---       #176
      0100000AH   LINE      CODE     ---       #178
      ---         BLOCKEND  ---      ---       LVL=0

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #183
      0100000EH   LINE      CODE     ---       #185
      01000010H   LINE      CODE     ---       #186
      01000012H   LINE      CODE     ---       #188
      ---         BLOCKEND  ---      ---       LVL=0

      01000076H   BLOCK     CODE     ---       LVL=0
      01000091H   BLOCK     CODE     NEAR LAB  LVL=1
      0000000DH   SYMBOL    DATA     BYTE      i
      0000000EH   SYMBOL    DATA     BYTE      checksum
      ---         BLOCKEND  ---      ---       LVL=1
      01000076H   LINE      CODE     ---       #194
      01000091H   LINE      CODE     ---       #196
      01000094H   LINE      CODE     ---       #198
      01000097H   LINE      CODE     ---       #200
      0100009AH   LINE      CODE     ---       #201
      0100009AH   LINE      CODE     ---       #202
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 21


      0100009CH   LINE      CODE     ---       #204
      0100009FH   LINE      CODE     ---       #205
      0100009FH   LINE      CODE     ---       #206
      010000ADH   LINE      CODE     ---       #207
      010000ADH   LINE      CODE     ---       #210
      010000BAH   LINE      CODE     ---       #211
      010000C0H   LINE      CODE     ---       #212
      010000C2H   LINE      CODE     ---       #214
      010000C2H   LINE      CODE     ---       #215
      010000D0H   LINE      CODE     ---       #216
      010000D0H   LINE      CODE     ---       #217
      010000DDH   LINE      CODE     ---       #218
      010000E1H   LINE      CODE     ---       #219
      010000E6H   LINE      CODE     ---       #220
      010000EAH   LINE      CODE     ---       #221
      010000ECH   LINE      CODE     ---       #222
      010000EEH   LINE      CODE     ---       #227
      010000EEH   LINE      CODE     ---       #228
      010000EEH   LINE      CODE     ---       #229
      010000EEH   LINE      CODE     ---       #231
      010000F4H   LINE      CODE     ---       #232
      010000F4H   LINE      CODE     ---       #233
      010000F6H   LINE      CODE     ---       #238
      01000101H   LINE      CODE     ---       #239
      01000101H   LINE      CODE     ---       #240
      01000106H   LINE      CODE     ---       #242
      01000108H   LINE      CODE     ---       #248
      0100010EH   LINE      CODE     ---       #249
      0100011CH   LINE      CODE     ---       #250
      0100011CH   LINE      CODE     ---       #251
      01000136H   LINE      CODE     ---       #252
      0100013AH   LINE      CODE     ---       #255
      0100014BH   LINE      CODE     ---       #256
      0100014BH   LINE      CODE     ---       #257
      01000151H   LINE      CODE     ---       #258
      01000177H   LINE      CODE     ---       #259
      01000177H   LINE      CODE     ---       #260
      01000197H   LINE      CODE     ---       #261
      01000197H   LINE      CODE     ---       #262
      010001B5H   LINE      CODE     ---       #263
      010001B5H   LINE      CODE     ---       #264
      010001B5H   LINE      CODE     ---       #265
      010001B5H   LINE      CODE     ---       #266
      010001B5H   LINE      CODE     ---       #267
      010001B5H   LINE      CODE     ---       #268
      010001B5H   LINE      CODE     ---       #269
      010001B7H   LINE      CODE     ---       #270
      010001B7H   LINE      CODE     ---       #272
      010001B7H   LINE      CODE     ---       #273
      010001B7H   LINE      CODE     ---       #274
      010001B7H   LINE      CODE     ---       #275
      010001B7H   LINE      CODE     ---       #276
      010001B7H   LINE      CODE     ---       #277
      010001B7H   LINE      CODE     ---       #278
      010001B7H   LINE      CODE     ---       #279
      010001B9H   LINE      CODE     ---       #281
      010001B9H   LINE      CODE     ---       #282
      010001C9H   LINE      CODE     ---       #283
      010001C9H   LINE      CODE     ---       #284
      010001E7H   LINE      CODE     ---       #285
      010001E7H   LINE      CODE     ---       #286
      010001E9H   LINE      CODE     ---       #287
      010001EBH   LINE      CODE     ---       #288
      010001EDH   LINE      CODE     ---       #289
      010001F2H   LINE      CODE     ---       #290
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 22


      010001F4H   LINE      CODE     ---       #291
      010001F6H   LINE      CODE     ---       #292
      010001F6H   LINE      CODE     ---       #294
      010001F6H   LINE      CODE     ---       #295
      010001F6H   LINE      CODE     ---       #296
      010001F6H   LINE      CODE     ---       #297
      010001F6H   LINE      CODE     ---       #298
      010001F6H   LINE      CODE     ---       #299
      010001F6H   LINE      CODE     ---       #300
      010001F6H   LINE      CODE     ---       #301
      010001F6H   LINE      CODE     ---       #302
      010001F6H   LINE      CODE     ---       #303
      010001F8H   LINE      CODE     ---       #305
      010001F8H   LINE      CODE     ---       #306
      010001FDH   LINE      CODE     ---       #307
      01000201H   LINE      CODE     ---       #308
      01000203H   LINE      CODE     ---       #309
      01000205H   LINE      CODE     ---       #310
      01000209H   LINE      CODE     ---       #311
      01000209H   LINE      CODE     ---       #312
      0100020EH   LINE      CODE     ---       #350
      0100020EH   LINE      CODE     ---       #351
      0100020EH   LINE      CODE     ---       #352
      ---         BLOCKEND  ---      ---       LVL=0

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #356
      01000016H   LINE      CODE     ---       #358
      01000018H   LINE      CODE     ---       #359
      0100001AH   LINE      CODE     ---       #361
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #365
      01000026H   LINE      CODE     ---       #367
      01000029H   LINE      CODE     ---       #368
      ---         BLOCKEND  ---      ---       LVL=0

      0100004EH   BLOCK     CODE     ---       LVL=0
      0100004EH   LINE      CODE     ---       #372
      0100004EH   LINE      CODE     ---       #374
      01000051H   LINE      CODE     ---       #375
      01000054H   LINE      CODE     ---       #376
      01000056H   LINE      CODE     ---       #378
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #385
      0100002EH   LINE      CODE     ---       #387
      01000031H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0

      01000046H   BLOCK     CODE     ---       LVL=0
      01000046H   LINE      CODE     ---       #393
      01000046H   LINE      CODE     ---       #395
      01000049H   LINE      CODE     ---       #397
      ---         BLOCKEND  ---      ---       LVL=0

      01000057H   BLOCK     CODE     ---       LVL=0
      01000057H   LINE      CODE     ---       #401
      01000057H   LINE      CODE     ---       #403
      0100005AH   LINE      CODE     ---       #405
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UARTDRIVER
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 23


      01000962H   PUBLIC    CODE     ---       _UartConf
      0100006DH   PUBLIC    CODE     ---       UartIntEnable
      0100005EH   PUBLIC    CODE     ---       UartIntDisable
      01000066H   PUBLIC    CODE     ---       UARTSendOn
      0100001EH   PUBLIC    CODE     ---       UARTReceiveOn
      01000855H   PUBLIC    CODE     ---       _UartBaudSet
      01000036H   PUBLIC    CODE     ---       UartInit
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H.0 SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H.0 SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H.0 SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 24


      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H.0 SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H.0 SFRSYM    DATA     BIT       IT0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 25


      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #18
      01000036H   LINE      CODE     ---       #19
      01000036H   LINE      CODE     ---       #20
      01000039H   LINE      CODE     ---       #21
      0100003CH   LINE      CODE     ---       #23
      0100003FH   LINE      CODE     ---       #24
      01000042H   LINE      CODE     ---       #25
      01000045H   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      01000855H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      baudrate
      01000855H   BLOCK     CODE     NEAR LAB  LVL=1
      0200005BH   SYMBOL    XDATA    WORD      iBaudRate
      ---         BLOCKEND  ---      ---       LVL=1
      01000855H   LINE      CODE     ---       #34
      01000855H   LINE      CODE     ---       #35
      01000855H   LINE      CODE     ---       #38
      01000873H   LINE      CODE     ---       #39
      01000876H   LINE      CODE     ---       #40
      01000879H   LINE      CODE     ---       #41
      0100087BH   LINE      CODE     ---       #42
      0100087DH   LINE      CODE     ---       #43
      01000880H   LINE      CODE     ---       #44
      01000883H   LINE      CODE     ---       #45
      01000886H   LINE      CODE     ---       #46
      01000888H   LINE      CODE     ---       #48
      0100088BH   LINE      CODE     ---       #50
      0100088EH   LINE      CODE     ---       #51
      01000890H   LINE      CODE     ---       #52
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #55
      0100001EH   LINE      CODE     ---       #56
      0100001EH   LINE      CODE     ---       #57
      01000020H   LINE      CODE     ---       #58
      01000022H   LINE      CODE     ---       #59
      ---         BLOCKEND  ---      ---       LVL=0

      01000066H   BLOCK     CODE     ---       LVL=0
      01000066H   LINE      CODE     ---       #62
      01000066H   LINE      CODE     ---       #63
      01000066H   LINE      CODE     ---       #64
      01000068H   LINE      CODE     ---       #65
      0100006AH   LINE      CODE     ---       #66
      0100006CH   LINE      CODE     ---       #67
      ---         BLOCKEND  ---      ---       LVL=0

      0100005EH   BLOCK     CODE     ---       LVL=0
      0100005EH   LINE      CODE     ---       #70
      0100005EH   LINE      CODE     ---       #71
      0100005EH   LINE      CODE     ---       #72
      01000061H   LINE      CODE     ---       #73
      ---         BLOCKEND  ---      ---       LVL=0

      0100006DH   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.93.0                                                        06/04/2025  11:18:48  PAGE 26


      0100006DH   LINE      CODE     ---       #77
      0100006DH   LINE      CODE     ---       #78
      0100006DH   LINE      CODE     ---       #79
      01000070H   LINE      CODE     ---       #80
      ---         BLOCKEND  ---      ---       LVL=0

      01000962H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      state
      01000962H   LINE      CODE     ---       #86
      01000962H   LINE      CODE     ---       #87
      01000962H   LINE      CODE     ---       #88
      01000965H   LINE      CODE     ---       #90
      0100096BH   LINE      CODE     ---       #91
      0100096BH   LINE      CODE     ---       #92
      0100096BH   LINE      CODE     ---       #93
      0100096DH   LINE      CODE     ---       #94
      01000973H   LINE      CODE     ---       #95
      01000973H   LINE      CODE     ---       #96
      01000976H   LINE      CODE     ---       #97
      01000976H   LINE      CODE     ---       #99
      01000976H   LINE      CODE     ---       #100
      01000979H   LINE      CODE     ---       #101
      01000979H   LINE      CODE     ---       #102
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_INIT
      010004C5H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000360H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      0100038DH   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?SLDIV
      0100074DH   PUBLIC    CODE     ---       ?C?SLDIV

      ---         MODULE    ---      ---       ?C?ULCMP
      0100039FH   PUBLIC    CODE     ---       ?C?ULCMP

      ---         MODULE    ---      ---       ?C?ULDIV
      010003ECH   PUBLIC    CODE     ---       ?C?ULDIV

Program Size: data=57.3 xdata=93 const=14 code=2421
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
