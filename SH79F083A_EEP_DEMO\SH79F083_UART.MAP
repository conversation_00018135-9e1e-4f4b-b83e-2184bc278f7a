LX51 LINKER/LOCATER V4.66.41.0                                                          07/22/2019  14:49:27  PAGE 1


LX51 LINKER/LOCATER V4.66.41.0, INVOKED BY:
C:\KEIL_V5\C51\BIN\LX51.EXE STARTUP.obj, main.obj, EEPROM.obj TO SH79F083_UART


CPU MODE:     8051 MODE
MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  STARTUP.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.54
  EEPROM.obj (EEPROM)
         COMMENT TYPE 0: C51 V9.54
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL_V5\C51\LIB\C51S.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  SH79F083_UART (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   000345H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   000005H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000000H.6 BIT
I:000000H   I:000000H   I:00007FH   00001AH   DATA


MEMORY MAP OF MODULE:  SH79F083_UART (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000019H   000012H   BYTE   UNIT     DATA           _DATA_GROUP_
00001AH.0 00001FH.7 000006H.0 ---    ---      **GAP**
000020H.0 000020H.5 000000H.6 BIT    UNIT     BIT            _BIT_GROUP_
000020H.6 000020H   000000H.2 ---    ---      **GAP**
000021H   000021H   000001H   BYTE   UNIT     IDATA          ?STACK

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   0000BCH   0000BAH   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0000BDH   000153H   000097H   BYTE   UNIT     CODE           ?PR?_WRITEEEPROM?EEPROM
000154H   0001B4H   000061H   BYTE   UNIT     CODE           ?C?LIB_CODE
0001B5H   0001F7H   000043H   BYTE   UNIT     CODE           ?PR?SYSINIT?MAIN
0001F8H   000239H   000042H   BYTE   UNIT     CODE           ?PR?_WRITE_CHAR?EEPROM
00023AH   000277H   00003EH   BYTE   UNIT     CODE           ?PR?_READEEPROM?EEPROM
000278H   0002ACH   000035H   BYTE   UNIT     CODE           ?PR?_EEPROMSECTORERASE?EEPROM
0002ADH   0002CDH   000021H   BYTE   UNIT     CODE           ?PR?_READ_CHAR?EEPROM
0002CEH   0002EDH   000020H   BYTE   UNIT     CODE           ?PR?_WRITE_CHAR_CHECK?EEPROM
0002EEH   000307H   00001AH   BYTE   UNIT     CODE           ?PR?T0INITIAL?MAIN
000308H   000321H   00001AH   BYTE   UNIT     CODE           ?PR?T1INITIAL?MAIN
000322H   000338H   000017H   BYTE   UNIT     CODE           ?PR?CLR_DATARAM?MAIN
000339H   000344H   00000CH   BYTE   UNIT     CODE           ?C_C51STARTUP

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000004H   000005H   BYTE   UNIT     XDATA          _XDATA_GROUP_
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 2





OVERLAY MAP OF MODULE:   SH79F083_UART (?C_STARTUP)


FUNCTION/MODULE                 BIT_GROUP   DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE     START  STOP  START  STOP  START  STOP
====================================================================
?C_C51STARTUP                  ----- -----  ----- -----  ----- -----
  +--> MAIN/MAIN

MAIN/MAIN                      ----- -----  ----- -----  0000H 0003H
  +--> SYSINIT/MAIN
  +--> _READEEPROM/EEPROM
  +--> _WRITEEEPROM/EEPROM

SYSINIT/MAIN                   ----- -----  ----- -----  ----- -----
  +--> T0INITIAL/MAIN
  +--> T1INITIAL/MAIN
  +--> CLR_DATARAM/MAIN

T0INITIAL/MAIN                 ----- -----  ----- -----  ----- -----

T1INITIAL/MAIN                 ----- -----  ----- -----  ----- -----

CLR_DATARAM/MAIN               ----- -----  ----- -----  ----- -----

_READEEPROM/EEPROM             20H.0 20H.0  0008H 000DH  ----- -----
  +--> _READ_CHAR/EEPROM

_READ_CHAR/EEPROM              20H.5 20H.5  ----- -----  0004H 0004H

_WRITEEEPROM/EEPROM            20H.0 20H.2  0008H 0011H  ----- -----
  +--> _EEPROMSECTORERASE/EEPROM
  +--> _WRITE_CHAR_CHECK/EEPROM

_EEPROMSECTORERASE/EEPROM      20H.3 20H.3  ----- -----  ----- -----

_WRITE_CHAR_CHECK/EEPROM       20H.3 20H.4  0012H 0015H  ----- -----
  +--> _WRITE_CHAR/EEPROM
  +--> _READ_CHAR/EEPROM

_WRITE_CHAR/EEPROM             20H.5 20H.5  0016H 0019H  ----- -----



PUBLIC SYMBOLS OF MODULE:  SH79F083_UART (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00000008H   DATA     BYTE      ?_ReadEEprom?BYTE
      00000016H   DATA     BYTE      ?_Write_char?BYTE
      00000012H   DATA     BYTE      ?_Write_char_check?BYTE
      00000008H   DATA     BYTE      ?_WriteEEprom?BYTE
      01000154H   CODE     ---       ?C?CLDOPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      01000193H   CODE     ---       ?C?CSTOPTR
      01000181H   CODE     ---       ?C?CSTPTR
      00000000H   NUMBER   ---       ?C?XDATASEG
      01000003H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      01000278H   CODE     ---       _EEPROMSectorErase
      010002ADH   CODE     ---       _Read_char
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 3


      0100023AH   CODE     ---       _ReadEEprom
      010001F8H   CODE     ---       _Write_char
      010002CEH   CODE     ---       _Write_char_check
      010000BDH   CODE     ---       _WriteEEprom
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
*SFR* 00000095H   DATA     BYTE      ADCH
*SFR* 00000093H   DATA     BYTE      ADCON
*SFR* 00000097H   DATA     BYTE      ADDH
*SFR* 00000096H   DATA     BYTE      ADDL
*SFR* 00000094H   DATA     BYTE      ADT
*SFR* 000000F1H   DATA     BYTE      AUXC
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000C8H.1 DATA     BIT       C_T2
*SFR* 000000B2H   DATA     BYTE      CLKCON
*SFR* 000000BDH   DATA     BYTE      CLKLO
*SFR* 000000BEH   DATA     BYTE      CLKRC0
*SFR* 000000BFH   DATA     BYTE      CLKRC1
      01000322H   CODE     ---       CLR_DataRAM
*SFR* 000000C8H   DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000A8H.6 DATA     BIT       EADC
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H   DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000C8H.3 DATA     BIT       EXEN2
*SFR* 000000E8H   DATA     BYTE      EXF0
*SFR* 000000C8H.6 DATA     BIT       EXF2
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000A7H   DATA     BYTE      FLASHCON
*SFR* 000000F2H   DATA     BYTE      IB_CON1
*SFR* 000000F3H   DATA     BYTE      IB_CON2
*SFR* 000000F4H   DATA     BYTE      IB_CON3
*SFR* 000000F5H   DATA     BYTE      IB_CON4
*SFR* 000000F6H   DATA     BYTE      IB_CON5
*SFR* 000000FCH   DATA     BYTE      IB_DATA
*SFR* 000000FBH   DATA     BYTE      IB_OFFSET
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000E8H   DATA     BIT       IE2
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000A9H   DATA     BYTE      IEN1
*SFR* 00000086H   DATA     BYTE      INSCON
*SFR* 000000B4H   DATA     BYTE      IPH0
*SFR* 000000B5H   DATA     BYTE      IPH1
*SFR* 000000B8H   DATA     BYTE      IPL0
*SFR* 000000B9H   DATA     BYTE      IPL1
*SFR* 00000088H   DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000E8H.2 DATA     BIT       IT20
*SFR* 000000E8H.3 DATA     BIT       IT21
*SFR* 000000B3H   DATA     BYTE      LPDCON
      01000003H   CODE     ---       main
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H   DATA     BIT       P
*SFR* 00000090H   DATA     BYTE      P1
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 4


*SFR* 00000090H   DATA     BIT       P1_0
*SFR* 00000090H.1 DATA     BIT       P1_1
*SFR* 00000090H.2 DATA     BIT       P1_2
*SFR* 00000090H.3 DATA     BIT       P1_3
*SFR* 00000090H.4 DATA     BIT       P1_4
*SFR* 00000090H.5 DATA     BIT       P1_5
*SFR* 00000090H.6 DATA     BIT       P1_6
*SFR* 00000090H.7 DATA     BIT       P1_7
*SFR* 000000EAH   DATA     BYTE      P1M0
*SFR* 000000E2H   DATA     BYTE      P1M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H   DATA     BIT       P3_0
*SFR* 000000B0H.1 DATA     BIT       P3_1
*SFR* 000000B0H.2 DATA     BIT       P3_2
*SFR* 000000B0H.3 DATA     BIT       P3_3
*SFR* 000000B0H.4 DATA     BIT       P3_4
*SFR* 000000B0H.5 DATA     BIT       P3_5
*SFR* 000000B0H.7 DATA     BIT       P3_7
*SFR* 000000ECH   DATA     BYTE      P3M0
*SFR* 000000E4H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H   DATA     BIT       P4_0
*SFR* 000000C0H.1 DATA     BIT       P4_1
*SFR* 000000C0H.2 DATA     BIT       P4_2
*SFR* 000000EDH   DATA     BYTE      P4M0
*SFR* 000000E5H   DATA     BYTE      P4M1
*SFR* 000000B8H.6 DATA     BIT       PADCL
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B8H.4 DATA     BIT       PSL
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0L
*SFR* 000000B8H.3 DATA     BIT       PT1L
*SFR* 000000B8H.5 DATA     BIT       PT2L
*SFR* 000000D1H   DATA     BYTE      PWMCON
*SFR* 000000D3H   DATA     BYTE      PWMD
*SFR* 000000D2H   DATA     BYTE      PWMP
*SFR* 000000B8H   DATA     BIT       PX0L
*SFR* 000000B8H.2 DATA     BIT       PX1L
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000CBH   DATA     BYTE      RCAP2H
*SFR* 000000CAH   DATA     BYTE      RCAP2L
*SFR* 000000C8H.5 DATA     BIT       RCLK
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 00000098H   DATA     BIT       RI
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 000000B1H   DATA     BYTE      RSTSTAT
*SFR* 0000009AH   DATA     BYTE      SADDR
*SFR* 0000009BH   DATA     BYTE      SADEN
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 00000098H.7 DATA     BIT       SM0_FE
*SFR* 00000098H.6 DATA     BIT       SM1_RXOV
*SFR* 00000098H.5 DATA     BIT       SM2_TXCOL
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 0000008EH   DATA     BYTE      SUSLO
      010001B5H   CODE     ---       SysInit
      010002EEH   CODE     ---       T0Initial
      01000308H   CODE     ---       T1Initial
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C9H   DATA     BYTE      T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.4 DATA     BIT       TCLK
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 000000CEH   DATA     BYTE      TCON1
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 5


*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C8H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 00000098H.1 DATA     BIT       TI
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C8H.2 DATA     BIT       TR2
*SFR* 000000F7H   DATA     BYTE      XPAGE



SYMBOL TABLE OF MODULE:  SH79F083_UART (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000080H   SYMBOL    NUMBER   ---       IDATALEN
      0100033CH   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      01000339H   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XDATALEN
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      01000339H   LINE      CODE     ---       #133
      0100033BH   LINE      CODE     ---       #134
      0100033CH   LINE      CODE     ---       #135
      0100033DH   LINE      CODE     ---       #136
      0100033FH   LINE      CODE     ---       #185
      01000342H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       MAIN
      01000003H   PUBLIC    CODE     ---       main
      01000308H   PUBLIC    CODE     ---       T1Initial
      010002EEH   PUBLIC    CODE     ---       T0Initial
      010001B5H   PUBLIC    CODE     ---       SysInit
      01000322H   PUBLIC    CODE     ---       CLR_DataRAM
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 6


      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H   SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H   SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000BEH   SFRSYM    DATA     BYTE      CLKRC0
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000BFH   SFRSYM    DATA     BYTE      CLKRC1
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B8H   SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 7


      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H   SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H   SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H   SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000BDH   SFRSYM    DATA     BYTE      CLKLO
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 8



      01000322H   BLOCK     CODE     ---       LVL=0
      01000322H   BLOCK     CODE     NEAR LAB  LVL=1
      00000001H   SYMBOL    DATA     ---       p
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01000322H   LINE      CODE     ---       #43
      01000322H   LINE      CODE     ---       #44
      01000322H   LINE      CODE     ---       #48
      01000328H   LINE      CODE     ---       #49
      01000338H   LINE      CODE     ---       #50
      ---         BLOCKEND  ---      ---       LVL=0

      010001B5H   BLOCK     CODE     ---       LVL=0
      010001B5H   LINE      CODE     ---       #55
      010001B5H   LINE      CODE     ---       #56
      010001B5H   LINE      CODE     ---       #57
      010001B7H   LINE      CODE     ---       #59
      010001BAH   LINE      CODE     ---       #60
      010001BDH   LINE      CODE     ---       #61
      010001BFH   LINE      CODE     ---       #63
      010001C2H   LINE      CODE     ---       #64
      010001C5H   LINE      CODE     ---       #66
      010001C7H   LINE      CODE     ---       #67
      010001CAH   LINE      CODE     ---       #69
      010001CCH   LINE      CODE     ---       #70
      010001CFH   LINE      CODE     ---       #73
      010001D1H   LINE      CODE     ---       #80
      010001D4H   LINE      CODE     ---       #81
      010001D7H   LINE      CODE     ---       #82
      010001D9H   LINE      CODE     ---       #83
      010001DBH   LINE      CODE     ---       #87
      010001DEH   LINE      CODE     ---       #90
      010001E1H   LINE      CODE     ---       #97
      010001E4H   LINE      CODE     ---       #101
      010001E7H   LINE      CODE     ---       #104
      010001E9H   LINE      CODE     ---       #106
      010001ECH   LINE      CODE     ---       #109
      010001EFH   LINE      CODE     ---       #110
      010001F2H   LINE      CODE     ---       #112
      010001F5H   LINE      CODE     ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      010002EEH   BLOCK     CODE     ---       LVL=0
      010002EEH   LINE      CODE     ---       #148
      010002EEH   LINE      CODE     ---       #149
      010002EEH   LINE      CODE     ---       #150
      010002F1H   LINE      CODE     ---       #152
      010002F4H   LINE      CODE     ---       #153
      010002F7H   LINE      CODE     ---       #156
      010002FAH   LINE      CODE     ---       #157
      010002FDH   LINE      CODE     ---       #160
      01000300H   LINE      CODE     ---       #161
      01000303H   LINE      CODE     ---       #162
      01000305H   LINE      CODE     ---       #163
      01000307H   LINE      CODE     ---       #164
      ---         BLOCKEND  ---      ---       LVL=0

      01000308H   BLOCK     CODE     ---       LVL=0
      01000308H   LINE      CODE     ---       #168
      01000308H   LINE      CODE     ---       #169
      01000308H   LINE      CODE     ---       #170
      0100030BH   LINE      CODE     ---       #172
      0100030EH   LINE      CODE     ---       #173
      01000311H   LINE      CODE     ---       #176
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 9


      01000314H   LINE      CODE     ---       #177
      01000317H   LINE      CODE     ---       #180
      0100031AH   LINE      CODE     ---       #181
      0100031DH   LINE      CODE     ---       #182
      0100031FH   LINE      CODE     ---       #183
      01000321H   LINE      CODE     ---       #184
      ---         BLOCKEND  ---      ---       LVL=0

      01000003H   BLOCK     CODE     ---       LVL=0
      01000003H   BLOCK     CODE     NEAR LAB  LVL=1
      02000000H   SYMBOL    XDATA    ---       temp
      ---         BLOCKEND  ---      ---       LVL=1
      01000003H   LINE      CODE     ---       #191
      01000003H   LINE      CODE     ---       #192
      01000003H   LINE      CODE     ---       #196
      01000005H   LINE      CODE     ---       #197
      01000008H   LINE      CODE     ---       #200
      0100000AH   LINE      CODE     ---       #202
      0100000AH   LINE      CODE     ---       #203
      0100000AH   LINE      CODE     ---       #204
      0100000DH   LINE      CODE     ---       #206
      01000012H   LINE      CODE     ---       #207
      01000014H   LINE      CODE     ---       #208
      01000016H   LINE      CODE     ---       #209
      01000018H   LINE      CODE     ---       #210
      01000027H   LINE      CODE     ---       #211
      01000028H   LINE      CODE     ---       #212
      0100002EH   LINE      CODE     ---       #213
      01000030H   LINE      CODE     ---       #214
      01000032H   LINE      CODE     ---       #215
      01000034H   LINE      CODE     ---       #216
      01000047H   LINE      CODE     ---       #217
      0100004CH   LINE      CODE     ---       #218
      0100004EH   LINE      CODE     ---       #219
      01000050H   LINE      CODE     ---       #220
      01000052H   LINE      CODE     ---       #221
      01000061H   LINE      CODE     ---       #222
      01000062H   LINE      CODE     ---       #225
      01000067H   LINE      CODE     ---       #226
      01000069H   LINE      CODE     ---       #227
      0100006BH   LINE      CODE     ---       #228
      0100006DH   LINE      CODE     ---       #229
      0100007DH   LINE      CODE     ---       #230
      0100007EH   LINE      CODE     ---       #231
      01000084H   LINE      CODE     ---       #232
      01000086H   LINE      CODE     ---       #233
      01000088H   LINE      CODE     ---       #234
      0100008AH   LINE      CODE     ---       #235
      0100009EH   LINE      CODE     ---       #236
      010000A3H   LINE      CODE     ---       #237
      010000A5H   LINE      CODE     ---       #238
      010000A7H   LINE      CODE     ---       #239
      010000A9H   LINE      CODE     ---       #240
      010000B9H   LINE      CODE     ---       #241
      010000BAH   LINE      CODE     ---       #243
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EEPROM
      00000008H   PUBLIC    DATA     BYTE      ?_WriteEEprom?BYTE
      010000BDH   PUBLIC    CODE     ---       _WriteEEprom
      00000012H   PUBLIC    DATA     BYTE      ?_Write_char_check?BYTE
      010002CEH   PUBLIC    CODE     ---       _Write_char_check
      00000016H   PUBLIC    DATA     BYTE      ?_Write_char?BYTE
      010001F8H   PUBLIC    CODE     ---       _Write_char
      01000278H   PUBLIC    CODE     ---       _EEPROMSectorErase
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 10


      00000008H   PUBLIC    DATA     BYTE      ?_ReadEEprom?BYTE
      0100023AH   PUBLIC    CODE     ---       _ReadEEprom
      010002ADH   PUBLIC    CODE     ---       _Read_char
      000000EAH   SFRSYM    DATA     BYTE      P1M0
      000000F3H   SFRSYM    DATA     BYTE      IB_CON2
      000000E8H.2 SFRSYM    DATA     BIT       IT20
      000000E2H   SFRSYM    DATA     BYTE      P1M1
      000000F4H   SFRSYM    DATA     BYTE      IB_CON3
      000000E8H.3 SFRSYM    DATA     BIT       IT21
      000000ECH   SFRSYM    DATA     BYTE      P3M0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000F5H   SFRSYM    DATA     BYTE      IB_CON4
      000000EDH   SFRSYM    DATA     BYTE      P4M0
      000000E4H   SFRSYM    DATA     BYTE      P3M1
      000000F6H   SFRSYM    DATA     BYTE      IB_CON5
      00000098H.6 SFRSYM    DATA     BIT       SM1_RXOV
      000000E5H   SFRSYM    DATA     BYTE      P4M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000FCH   SFRSYM    DATA     BYTE      IB_DATA
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000A8H.6 SFRSYM    DATA     BIT       EADC
      000000A9H   SFRSYM    DATA     BYTE      IEN1
      00000085H   SFRSYM    DATA     BYTE      DPH1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000095H   SFRSYM    DATA     BYTE      ADCH
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000097H   SFRSYM    DATA     BYTE      ADDH
      000000B4H   SFRSYM    DATA     BYTE      IPH0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000B0H   SFRSYM    DATA     BIT       P3_0
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      000000B5H   SFRSYM    DATA     BYTE      IPH1
      000000C0H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      000000E8H   SFRSYM    DATA     BYTE      EXF0
      000000C0H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      000000C0H.2 SFRSYM    DATA     BIT       P4_2
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      00000096H   SFRSYM    DATA     BYTE      ADDL
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      000000B9H   SFRSYM    DATA     BYTE      IPL1
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      0000008EH   SFRSYM    DATA     BYTE      SUSLO
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      00000098H   SFRSYM    DATA     BIT       RI
      000000D0H.7 SFRSYM    DATA     BIT       CY
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000B8H.1 SFRSYM    DATA     BIT       PT0L
      000000BEH   SFRSYM    DATA     BYTE      CLKRC0
      000000B8H.3 SFRSYM    DATA     BIT       PT1L
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000BFH   SFRSYM    DATA     BYTE      CLKRC1
      000000B8H.5 SFRSYM    DATA     BIT       PT2L
      00000081H   SFRSYM    DATA     BYTE      SP
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 11


      000000B8H   SFRSYM    DATA     BIT       PX0L
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000B8H.2 SFRSYM    DATA     BIT       PX1L
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000F1H   SFRSYM    DATA     BYTE      AUXC
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000FBH   SFRSYM    DATA     BYTE      IB_OFFSET
      000000B1H   SFRSYM    DATA     BYTE      RSTSTAT
      000000D3H   SFRSYM    DATA     BYTE      PWMD
      00000098H.7 SFRSYM    DATA     BIT       SM0_FE
      000000B2H   SFRSYM    DATA     BYTE      CLKCON
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      00000098H.5 SFRSYM    DATA     BIT       SM2_TXCOL
      000000E8H   SFRSYM    DATA     BIT       IE2
      000000F0H   SFRSYM    DATA     BYTE      B
      000000B3H   SFRSYM    DATA     BYTE      LPDCON
      000000C8H   SFRSYM    DATA     BIT       CP_RL2
      000000D2H   SFRSYM    DATA     BYTE      PWMP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      00000086H   SFRSYM    DATA     BYTE      INSCON
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H   SFRSYM    DATA     BIT       P
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000A7H   SFRSYM    DATA     BYTE      FLASHCON
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000D1H   SFRSYM    DATA     BYTE      PWMCON
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      00000094H   SFRSYM    DATA     BYTE      ADT
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000B8H.6 SFRSYM    DATA     BIT       PADCL
      00000093H   SFRSYM    DATA     BYTE      ADCON
      000000CEH   SFRSYM    DATA     BYTE      TCON1
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      000000C8H   SFRSYM    DATA     BYTE      T2CON
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 12


      0000009BH   SFRSYM    DATA     BYTE      SADEN
      000000B8H.4 SFRSYM    DATA     BIT       PSL
      0000009AH   SFRSYM    DATA     BYTE      SADDR
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000F7H   SFRSYM    DATA     BYTE      XPAGE
      000000BDH   SFRSYM    DATA     BYTE      CLKLO
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000F2H   SFRSYM    DATA     BYTE      IB_CON1

      010002ADH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      pagenum
      00000005H   SYMBOL    DATA     BYTE      address
      010002ADH   BLOCK     CODE     NEAR LAB  LVL=1
      02000004H   SYMBOL    XDATA    BYTE      temp
      00000020H.5 SYMBOL    BIT      BIT       EABak
      ---         BLOCKEND  ---      ---       LVL=1
      010002ADH   LINE      CODE     ---       #12
      010002ADH   LINE      CODE     ---       #13
      010002ADH   LINE      CODE     ---       #16
      010002AFH   LINE      CODE     ---       #18
      010002B3H   LINE      CODE     ---       #20
      010002B5H   LINE      CODE     ---       #22
      010002B8H   LINE      CODE     ---       #23
      010002C6H   LINE      CODE     ---       #24
      010002C9H   LINE      CODE     ---       #26
      010002CDH   LINE      CODE     ---       #28
      010002CDH   LINE      CODE     ---       #29
      ---         BLOCKEND  ---      ---       LVL=0

      0100023AH   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    DATA     ---       buff
      0000000BH   SYMBOL    DATA     BYTE      pagenum
      0000000CH   SYMBOL    DATA     BYTE      gbHeadAddr
      0000000DH   SYMBOL    DATA     BYTE      len
      01000242H   BLOCK     CODE     NEAR LAB  LVL=1
      00000020H.0 SYMBOL    BIT      BIT       ReadErrFlag
      00000004H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      0100023AH   LINE      CODE     ---       #34
      01000242H   LINE      CODE     ---       #35
      01000242H   LINE      CODE     ---       #36
      01000244H   LINE      CODE     ---       #37
      01000244H   LINE      CODE     ---       #39
      0100024BH   LINE      CODE     ---       #40
      0100024BH   LINE      CODE     ---       #41
      0100024DH   LINE      CODE     ---       #42
      01000250H   LINE      CODE     ---       #43
      01000250H   LINE      CODE     ---       #45
      01000258H   LINE      CODE     ---       #46
      01000258H   LINE      CODE     ---       #47
      01000270H   LINE      CODE     ---       #48
      01000273H   LINE      CODE     ---       #49
      01000275H   LINE      CODE     ---       #50
      01000277H   LINE      CODE     ---       #51
      ---         BLOCKEND  ---      ---       LVL=0

      01000278H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      pagenum
      00000005H   SYMBOL    DATA     BYTE      FlashLock
      01000278H   BLOCK     CODE     NEAR LAB  LVL=1
      00000020H.3 SYMBOL    BIT      BIT       EABak
      010002A3H   SYMBOL    CODE     ---       Erase_Err
      ---         BLOCKEND  ---      ---       LVL=1
      01000278H   LINE      CODE     ---       #55
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 13


      01000278H   LINE      CODE     ---       #56
      01000278H   LINE      CODE     ---       #57
      0100027AH   LINE      CODE     ---       #59
      0100027EH   LINE      CODE     ---       #60
      01000280H   LINE      CODE     ---       #61
      01000283H   LINE      CODE     ---       #62
      01000286H   LINE      CODE     ---       #63
      01000289H   LINE      CODE     ---       #64
      0100028CH   LINE      CODE     ---       #65
      0100028FH   LINE      CODE     ---       #66
      01000294H   LINE      CODE     ---       #67
      01000294H   LINE      CODE     ---       #68
      01000297H   LINE      CODE     ---       #69
      01000299H   LINE      CODE     ---       #70
      01000299H   LINE      CODE     ---       #71
      0100029BH   LINE      CODE     ---       #72
      0100029EH   LINE      CODE     ---       #73
      0100029FH   LINE      CODE     ---       #74
      010002A0H   LINE      CODE     ---       #75
      010002A1H   LINE      CODE     ---       #76
      010002A2H   LINE      CODE     ---       #77
      010002A3H   LINE      CODE     ---       #78
      010002A3H   LINE      CODE     ---       #79
      010002A4H   LINE      CODE     ---       #80
      010002A6H   LINE      CODE     ---       #81
      010002A8H   LINE      CODE     ---       #82
      010002ACH   LINE      CODE     ---       #83
      ---         BLOCKEND  ---      ---       LVL=0

      010001F8H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      pagenum
      00000005H   SYMBOL    DATA     BYTE      address
      00000003H   SYMBOL    DATA     BYTE      uchardata
      00000019H   SYMBOL    DATA     BYTE      FlashLock
      010001F8H   BLOCK     CODE     NEAR LAB  LVL=1
      00000020H.5 SYMBOL    BIT      BIT       EABak
      0100022EH   SYMBOL    CODE     ---       Write_Err
      ---         BLOCKEND  ---      ---       LVL=1
      010001F8H   LINE      CODE     ---       #89
      010001F8H   LINE      CODE     ---       #90
      010001F8H   LINE      CODE     ---       #91
      010001FAH   LINE      CODE     ---       #93
      01000200H   LINE      CODE     ---       #94
      01000200H   LINE      CODE     ---       #95
      01000200H   LINE      CODE     ---       #96
      01000200H   LINE      CODE     ---       #98
      01000204H   LINE      CODE     ---       #99
      01000206H   LINE      CODE     ---       #100
      01000209H   LINE      CODE     ---       #101
      0100020BH   LINE      CODE     ---       #103
      0100020DH   LINE      CODE     ---       #105
      01000210H   LINE      CODE     ---       #106
      01000213H   LINE      CODE     ---       #107
      01000216H   LINE      CODE     ---       #108
      01000219H   LINE      CODE     ---       #109
      0100021FH   LINE      CODE     ---       #110
      0100021FH   LINE      CODE     ---       #111
      01000222H   LINE      CODE     ---       #112
      01000224H   LINE      CODE     ---       #113
      01000224H   LINE      CODE     ---       #114
      01000226H   LINE      CODE     ---       #115
      01000229H   LINE      CODE     ---       #117
      0100022AH   LINE      CODE     ---       #118
      0100022BH   LINE      CODE     ---       #119
      0100022CH   LINE      CODE     ---       #120
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 14


      0100022DH   LINE      CODE     ---       #121
      0100022EH   LINE      CODE     ---       #122
      0100022EH   LINE      CODE     ---       #123
      01000231H   LINE      CODE     ---       #124
      01000233H   LINE      CODE     ---       #125
      01000235H   LINE      CODE     ---       #126
      01000239H   LINE      CODE     ---       #127
      ---         BLOCKEND  ---      ---       LVL=0

      010002CEH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      pagenum
      00000002H   SYMBOL    DATA     BYTE      address
      00000004H   SYMBOL    DATA     BYTE      uchardata
      00000015H   SYMBOL    DATA     BYTE      FlashLock
      010002D4H   BLOCK     CODE     NEAR LAB  LVL=1
      00000020H.3 SYMBOL    BIT      BIT       EABak
      00000020H.4 SYMBOL    BIT      BIT       gbWriteOkFlag
      ---         BLOCKEND  ---      ---       LVL=1
      010002CEH   LINE      CODE     ---       #132
      010002D4H   LINE      CODE     ---       #133
      010002D4H   LINE      CODE     ---       #134
      010002D6H   LINE      CODE     ---       #135
      010002D8H   LINE      CODE     ---       #138
      010002DEH   LINE      CODE     ---       #140
      010002E5H   LINE      CODE     ---       #141
      010002E5H   LINE      CODE     ---       #142
      010002E7H   LINE      CODE     ---       #143
      010002E9H   LINE      CODE     ---       #145
      010002E9H   LINE      CODE     ---       #146
      010002EBH   LINE      CODE     ---       #147
      010002EBH   LINE      CODE     ---       #149
      010002EDH   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      010000BDH   BLOCK     CODE     ---       LVL=0
      00000008H   SYMBOL    DATA     ---       buff
      0000000BH   SYMBOL    DATA     BYTE      pagenum
      0000000CH   SYMBOL    DATA     BYTE      gbHeadAddr
      0000000DH   SYMBOL    DATA     BYTE      len
      0000000EH   SYMBOL    DATA     BYTE      FlashLock
      010000C5H   BLOCK     CODE     NEAR LAB  LVL=1
      00000020H.0 SYMBOL    BIT      BIT       gbEepromErrFlag
      00000020H.1 SYMBOL    BIT      BIT       gbWriteOkFlag
      00000020H.2 SYMBOL    BIT      BIT       gbWriteOkFlag1
      0000000FH   SYMBOL    DATA     BYTE      i
      00000010H   SYMBOL    DATA     BYTE      j
      00000011H   SYMBOL    DATA     BYTE      ErrCount
      ---         BLOCKEND  ---      ---       LVL=1
      010000BDH   LINE      CODE     ---       #154
      010000C5H   LINE      CODE     ---       #155
      010000C5H   LINE      CODE     ---       #156
      010000C7H   LINE      CODE     ---       #157
      010000C9H   LINE      CODE     ---       #158
      010000CBH   LINE      CODE     ---       #160
      010000D2H   LINE      CODE     ---       #163
      010000D9H   LINE      CODE     ---       #164
      010000D9H   LINE      CODE     ---       #165
      010000DBH   LINE      CODE     ---       #166
      010000DEH   LINE      CODE     ---       #167
      010000DEH   LINE      CODE     ---       #169
      010000E5H   LINE      CODE     ---       #171
      010000E8H   LINE      CODE     ---       #174
      010000F1H   LINE      CODE     ---       #175
      010000F1H   LINE      CODE     ---       #176
      010000F4H   LINE      CODE     ---       #177
LX51 LINKER/LOCATER V4.66.41.0                                                        07/22/2019  14:49:27  PAGE 15


      01000110H   LINE      CODE     ---       #178
      01000110H   LINE      CODE     ---       #179
      01000112H   LINE      CODE     ---       #180
      01000115H   LINE      CODE     ---       #181
      01000117H   LINE      CODE     ---       #183
      01000117H   LINE      CODE     ---       #184
      01000119H   LINE      CODE     ---       #186
      01000120H   LINE      CODE     ---       #187
      01000120H   LINE      CODE     ---       #188
      0100012DH   LINE      CODE     ---       #189
      0100012DH   LINE      CODE     ---       #190
      0100012FH   LINE      CODE     ---       #191
      01000131H   LINE      CODE     ---       #192
      01000131H   LINE      CODE     ---       #193
      01000131H   LINE      CODE     ---       #195
      01000133H   LINE      CODE     ---       #196
      0100013AH   LINE      CODE     ---       #197
      0100013AH   LINE      CODE     ---       #198
      0100013DH   LINE      CODE     ---       #199
      0100013FH   LINE      CODE     ---       #200
      0100013FH   LINE      CODE     ---       #201
      0100013FH   LINE      CODE     ---       #202
      01000145H   LINE      CODE     ---       #204
      0100014BH   LINE      CODE     ---       #205
      0100014BH   LINE      CODE     ---       #206
      0100014DH   LINE      CODE     ---       #207
      0100014FH   LINE      CODE     ---       #209
      0100014FH   LINE      CODE     ---       #210
      01000151H   LINE      CODE     ---       #211
      01000151H   LINE      CODE     ---       #213
      01000153H   LINE      CODE     ---       #214
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000154H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000181H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000193H   PUBLIC    CODE     ---       ?C?CSTOPTR

Program Size: data=27.6 xdata=5 const=0 code=837
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
