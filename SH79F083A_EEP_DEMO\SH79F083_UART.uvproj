<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x0</ToolsetNumber>
      <ToolsetName>MCS-51</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>SH79F083A</Device>
          <Vendor>Sinowealth</Vendor>
          <Cpu>IRAM(0 - 0xFF) IROM(0-0x1FFF) XRAM(0-0xFF) CLOCK(16600000)</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"LIB\STARTUP.A51"("Standard 8051 Startup Code")</StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>SH79F083A.H</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>Sinowealth\</RegisterFilePath>
          <DBRegisterFilePath>Sinowealth\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\</OutputDirectory>
          <OutputName>SH79F083_UART</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\</ListingPath>
          <HexFormatSelection>0</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
          <BankNo>65535</BankNo>
        </CommonProperty>
        <DllOption>
          <SimDllName>S8051.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DP51.DLL</SimDlgDll>
          <SimDlgDllArguments>-p51</SimDlgDllArguments>
          <TargetDllName>S8051.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TP51.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-p51</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>0</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>15</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\SH51A.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>0</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4107</DriverSelection>
          </Flash1>
          <bUseTDR>0</bUseTDR>
          <Flash2>BIN\SH51A.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <Target51>
          <Target51Misc>
            <MemoryModel>0</MemoryModel>
            <RTOS>0</RTOS>
            <RomSize>2</RomSize>
            <DataHold>0</DataHold>
            <XDataHold>0</XDataHold>
            <UseOnchipRom>0</UseOnchipRom>
            <UseOnchipArithmetic>0</UseOnchipArithmetic>
            <UseMultipleDPTR>0</UseMultipleDPTR>
            <UseOnchipXram>0</UseOnchipXram>
            <HadIRAM>1</HadIRAM>
            <HadXRAM>1</HadXRAM>
            <HadIROM>1</HadIROM>
            <Moda2>0</Moda2>
            <Moddp2>0</Moddp2>
            <Modp2>0</Modp2>
            <Mod517dp>0</Mod517dp>
            <Mod517au>0</Mod517au>
            <Mode2>0</Mode2>
            <useCB>0</useCB>
            <useXB>0</useXB>
            <useL251>1</useL251>
            <useA251>0</useA251>
            <Mx51>0</Mx51>
            <ModC812>0</ModC812>
            <ModCont>0</ModCont>
            <Lp51>0</Lp51>
            <useXBS>0</useXBS>
            <ModDA>0</ModDA>
            <ModAB2>0</ModAB2>
            <Mx51P>0</Mx51P>
            <hadXRAM2>0</hadXRAM2>
            <uocXram2>0</uocXram2>
            <ModC2>0</ModC2>
            <ModH2>0</ModH2>
            <Mdu_R515>0</Mdu_R515>
            <Mdu_F120>0</Mdu_F120>
            <Psoc>0</Psoc>
            <hadIROM2>0</hadIROM2>
            <ModSmx2>0</ModSmx2>
            <cBanks>0</cBanks>
            <xBanks>0</xBanks>
            <OnChipMemories>
              <RCB>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0xffff</Size>
              </RCB>
              <RXB>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </RXB>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocr1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocr1>
              <Ocr2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocr2>
              <Ocr3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocr3>
              <IRO>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x2000</Size>
              </IRO>
              <IRA>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x100</Size>
              </IRA>
              <XRA>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x100</Size>
              </XRA>
              <XRA512>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRA512>
              <IROM512>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM512>
            </OnChipMemories>
          </Target51Misc>
          <C51>
            <RegisterColoring>0</RegisterColoring>
            <VariablesInOrder>0</VariablesInOrder>
            <IntegerPromotion>1</IntegerPromotion>
            <uAregs>0</uAregs>
            <UseInterruptVector>1</UseInterruptVector>
            <Fuzzy>3</Fuzzy>
            <Optimize>8</Optimize>
            <WarningLevel>2</WarningLevel>
            <SizeSpeed>1</SizeSpeed>
            <ObjectExtend>1</ObjectExtend>
            <ACallAJmp>0</ACallAJmp>
            <InterruptVectorAddress>0</InterruptVectorAddress>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </C51>
          <Ax51>
            <UseMpl>0</UseMpl>
            <UseStandard>1</UseStandard>
            <UseCase>0</UseCase>
            <UseMod51>0</UseMod51>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Ax51>
          <Lx51>
            <useFile>0</useFile>
            <linkonly>0</linkonly>
            <UseMemoryFromTarget>1</UseMemoryFromTarget>
            <CaseSensitiveSymbols>0</CaseSensitiveSymbols>
            <WarningLevel>2</WarningLevel>
            <DataOverlaying>1</DataOverlaying>
            <OverlayString></OverlayString>
            <MiscControls></MiscControls>
            <DisableWarningNumbers></DisableWarningNumbers>
            <LinkerCmdFile></LinkerCmdFile>
            <Assign></Assign>
            <ReserveString></ReserveString>
            <CClasses></CClasses>
            <UserClasses></UserClasses>
            <CSection></CSection>
            <UserSection></UserSection>
            <CodeBaseAddress></CodeBaseAddress>
            <XDataBaseAddress></XDataBaseAddress>
            <PDataBaseAddress></PDataBaseAddress>
            <BitBaseAddress></BitBaseAddress>
            <DataBaseAddress></DataBaseAddress>
            <IDataBaseAddress></IDataBaseAddress>
            <Precede></Precede>
            <Stack></Stack>
            <CodeSegmentName></CodeSegmentName>
            <XDataSegmentName></XDataSegmentName>
            <BitSegmentName></BitSegmentName>
            <DataSegmentName></DataSegmentName>
            <IDataSegmentName></IDataSegmentName>
          </Lx51>
        </Target51>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source Group 1</GroupName>
          <Files>
            <File>
              <FileName>STARTUP.A51</FileName>
              <FileType>2</FileType>
              <FilePath>.\STARTUP.A51</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\main.c</FilePath>
            </File>
            <File>
              <FileName>EEPROM.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\EEPROM.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
