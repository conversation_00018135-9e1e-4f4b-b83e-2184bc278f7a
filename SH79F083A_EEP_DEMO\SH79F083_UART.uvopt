<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_opt.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
    <nMigrate>0</nMigrate>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>Target 1</TargetName>
    <ToolsetNumber>0x0</ToolsetNumber>
    <ToolsetName>MCS-51</ToolsetName>
    <TargetOption>
      <CLK51>16600000</CLK51>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>0</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>120</PageWidth>
        <PageLength>65</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>0</CpuCode>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>0</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>15</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>BIN\SH51A.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>SH51A</Key>
          <Name>-DSH79F083A -V-939523616 -N0 -********* -S0 -C0 -L0 -M0 -P0 -T0 -F0 -G0 -H0 -A0 -B0 -E0 -I0 -Z000000000000000000000000000000000000000000</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTP51</Key>
          <Name>(98=-1,-1,-1,-1,0)(82=-1,-1,-1,-1,0)(83=-1,-1,-1,-1,0)(84=-1,-1,-1,-1,0)(85=-1,-1,-1,-1,0)(80=-1,-1,-1,-1,0)(91=-1,-1,-1,-1,0)(92=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>SH51</Key>
          <Name>-DSH79F084A -V-2013224475 -O132871 -S0 -C0 -L0 -M0 -T0 -F0 -G0 -H0 -Z000000000000000000000000000000000000000000</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint>
        <Bp>
          <Number>0</Number>
          <Type>0</Type>
          <LineNumber>225</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16775370</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\225</Expression>
        </Bp>
        <Bp>
          <Number>1</Number>
          <Type>0</Type>
          <LineNumber>477</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16773947</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\476</Expression>
        </Bp>
        <Bp>
          <Number>2</Number>
          <Type>0</Type>
          <LineNumber>603</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774918</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\603</Expression>
        </Bp>
        <Bp>
          <Number>3</Number>
          <Type>0</Type>
          <LineNumber>653</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774724</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\648</Expression>
        </Bp>
        <Bp>
          <Number>4</Number>
          <Type>0</Type>
          <LineNumber>130</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16772229</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\130</Expression>
        </Bp>
        <Bp>
          <Number>5</Number>
          <Type>0</Type>
          <LineNumber>646</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774754</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\646</Expression>
        </Bp>
        <Bp>
          <Number>6</Number>
          <Type>0</Type>
          <LineNumber>154</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16772730</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\154</Expression>
        </Bp>
        <Bp>
          <Number>7</Number>
          <Type>0</Type>
          <LineNumber>190</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16771706</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\190</Expression>
        </Bp>
        <Bp>
          <Number>8</Number>
          <Type>0</Type>
          <LineNumber>738</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16773123</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\738</Expression>
        </Bp>
        <Bp>
          <Number>9</Number>
          <Type>0</Type>
          <LineNumber>1254</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16772876</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\1253</Expression>
        </Bp>
        <Bp>
          <Number>10</Number>
          <Type>0</Type>
          <LineNumber>456</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774092</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\456</Expression>
        </Bp>
        <Bp>
          <Number>11</Number>
          <Type>0</Type>
          <LineNumber>642</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774814</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\642</Expression>
        </Bp>
        <Bp>
          <Number>12</Number>
          <Type>0</Type>
          <LineNumber>609</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774905</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\609</Expression>
        </Bp>
        <Bp>
          <Number>13</Number>
          <Type>0</Type>
          <LineNumber>847</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16776184</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\847</Expression>
        </Bp>
        <Bp>
          <Number>14</Number>
          <Type>0</Type>
          <LineNumber>156</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16772728</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\156</Expression>
        </Bp>
        <Bp>
          <Number>15</Number>
          <Type>0</Type>
          <LineNumber>604</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16774913</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\INTERRUPT\604</Expression>
        </Bp>
        <Bp>
          <Number>16</Number>
          <Type>0</Type>
          <LineNumber>902</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16775943</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\902</Expression>
        </Bp>
        <Bp>
          <Number>17</Number>
          <Type>0</Type>
          <LineNumber>914</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16775910</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\914</Expression>
        </Bp>
        <Bp>
          <Number>18</Number>
          <Type>0</Type>
          <LineNumber>916</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16775904</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\916</Expression>
        </Bp>
        <Bp>
          <Number>19</Number>
          <Type>0</Type>
          <LineNumber>919</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16775901</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\919</Expression>
        </Bp>
        <Bp>
          <Number>20</Number>
          <Type>0</Type>
          <LineNumber>922</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16775849</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\922</Expression>
        </Bp>
        <Bp>
          <Number>21</Number>
          <Type>0</Type>
          <LineNumber>1084</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>-16777034</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>1</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\main.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression>\MAIN\1082</Expression>
        </Bp>
        <Bp>
          <Number>22</Number>
          <Type>0</Type>
          <LineNumber>473</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>0</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>0</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression></Expression>
        </Bp>
        <Bp>
          <Number>23</Number>
          <Type>0</Type>
          <LineNumber>599</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>0</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>0</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression></Expression>
        </Bp>
        <Bp>
          <Number>24</Number>
          <Type>0</Type>
          <LineNumber>600</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>0</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>0</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression></Expression>
        </Bp>
        <Bp>
          <Number>25</Number>
          <Type>0</Type>
          <LineNumber>605</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>0</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>0</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression></Expression>
        </Bp>
        <Bp>
          <Number>26</Number>
          <Type>0</Type>
          <LineNumber>638</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>0</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>0</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression></Expression>
        </Bp>
        <Bp>
          <Number>27</Number>
          <Type>0</Type>
          <LineNumber>649</LineNumber>
          <EnabledFlag>0</EnabledFlag>
          <Address>0</Address>
          <ByteObject>0</ByteObject>
          <HtxType>0</HtxType>
          <ManyObjects>0</ManyObjects>
          <SizeOfObject>0</SizeOfObject>
          <BreakByAccess>0</BreakByAccess>
          <BreakIfRCount>0</BreakIfRCount>
          <Filename>D:\DISKE\2016_Program\客户项目\天津李工\2Uart通信\CODE\V60_ID3210_HeadCodeLoop_V17_TestLED_OK_V40\V60_ID3210_HeadCodeLoop\interrupt.c</Filename>
          <ExecCommand></ExecCommand>
          <Expression></Expression>
        </Bp>
      </Breakpoint>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>gbUart0Buff</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>gbUart1Buff</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>LED2_On_Count</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>LED2_Off_Count</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>1</WinNumber>
          <ItemText>BUTTON_Value_Buff</ItemText>
        </Ww>
      </WatchWindow1>
      <MemoryWindow1>
        <Mm>
          <WinNumber>1</WinNumber>
          <SubType>0</SubType>
          <ItemText>X:0x500</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow1>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>Source Group 1</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\STARTUP.A51</PathWithFileName>
      <FilenameWithoutPath>STARTUP.A51</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>1</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>1</tvExp>
      <Focus>0</Focus>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\EEPROM.c</PathWithFileName>
      <FilenameWithoutPath>EEPROM.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
